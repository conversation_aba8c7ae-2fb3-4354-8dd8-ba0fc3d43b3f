# SuperFuture App - Navigation Guide

## Overview
The SuperFuture app now has a complete navigation system with routes and screens. The GetIt dependency injection issue has been resolved, and the app is fully functional with navigation between different features.

## Navigation Structure

### App Flow
```
Splash Screen → Login/Register → Home (Dashboard) → Feature Screens
```

### Route Definitions
All routes are defined in `lib/core/navigation/app_routes.dart`:

#### Authentication Routes
- `/` - Splash Screen (initial route)
- `/login` - Login Screen
- `/register` - Register Screen
- `/forgot-password` - Forgot Password (placeholder)

#### Main App Routes
- `/home` - Home Screen with bottom navigation
- `/dashboard` - Dashboard (same as home)

#### Feature Routes
- **Market**: `/market`, `/market/stock-detail`, `/market/search`
- **Wallet**: `/wallet`, `/wallet/transfer`, `/wallet/withdraw`, `/wallet/history`
- **Profile**: `/profile`, `/profile/edit`, `/profile/security`
- **Products**: `/products`, `/products/detail`, `/products/purchase`
- **Settings**: `/settings`, `/settings/notifications`, `/settings/language`

## How to Navigate

### Using AppRouter Helper Methods

```dart
import '../../../core/navigation/app_routes.dart';
import '../../../core/navigation/app_router.dart';

// Navigate to a new screen
AppRouter.navigateTo(context, AppRoutes.market);

// Navigate with arguments
AppRouter.navigateTo(
  context, 
  AppRoutes.stockDetail, 
  arguments: {'symbol': 'AAPL'}
);

// Navigate and clear stack (for login/logout)
AppRouter.navigateToAndClearStack(context, AppRoutes.home);

// Replace current screen
AppRouter.navigateToAndReplace(context, AppRoutes.login);

// Go back
AppRouter.goBack(context);

// Check if can go back
if (AppRouter.canGoBack(context)) {
  AppRouter.goBack(context);
}
```

### Direct Flutter Navigation

```dart
// Using Navigator directly
Navigator.pushNamed(context, AppRoutes.wallet);

// With arguments
Navigator.pushNamed(
  context, 
  AppRoutes.stockDetail,
  arguments: {'symbol': 'TSLA'}
);
```

## Current Screen Implementation Status

### ✅ Implemented Screens
- **SplashScreen** - App initialization and auth check
- **LoginScreen** - Login form placeholder with navigation
- **RegisterScreen** - Registration form placeholder
- **HomeScreen** - Main dashboard with bottom navigation tabs
- **MarketScreen** - Market overview placeholder
- **StockDetailScreen** - Individual stock details
- **WalletScreen** - Wallet overview with multiple wallet types
- **WalletTransferScreen** - Transfer functionality placeholder
- **ProfileScreen** - User profile placeholder
- **ProfileEditScreen** - Profile editing placeholder
- **ProductsScreen** - Contract products placeholder
- **SettingsScreen** - App settings placeholder

### 🔄 Placeholder Screens (Need Implementation)
Most screens currently show placeholder content with feature descriptions. They need:

1. **Form Implementation**
   - Login form with email/password
   - Registration form with validation
   - Profile editing forms
   - Transfer forms with amount validation

2. **Data Integration**
   - Connect to API services
   - Implement state management
   - Add real-time data updates

3. **UI Components**
   - Custom input fields
   - Loading states
   - Error handling
   - Success feedback

## Bottom Navigation Structure

The HomeScreen includes a bottom navigation with 4 tabs:

1. **Dashboard** - Portfolio overview and quick actions
2. **Market** - Market data and stock information
3. **Wallet** - Wallet management and transactions
4. **Profile** - User profile and settings

## Authentication Flow

### App Startup
1. **Splash Screen** loads and initializes dependencies
2. **Auth Check** determines if user is logged in
3. **Navigation** to either Home (if authenticated) or Login

### Login Process
1. User enters credentials on **Login Screen**
2. AuthCubit handles authentication
3. On success, navigate to **Home Screen**
4. On failure, show error message

### Logout Process
1. User triggers logout from profile/settings
2. AuthCubit clears authentication state
3. Navigate back to **Login Screen**

## Adding New Routes

### 1. Define Route
Add to `lib/core/navigation/app_routes.dart`:
```dart
static const String newFeature = '/new-feature';
```

### 2. Create Screen
Create screen file in appropriate feature folder:
```dart
class NewFeatureScreen extends StatelessWidget {
  const NewFeatureScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('New Feature')),
      body: const Center(child: Text('New Feature Content')),
    );
  }
}
```

### 3. Add Route Handler
Add to `lib/core/navigation/app_router.dart`:
```dart
case AppRoutes.newFeature:
  return MaterialPageRoute(builder: (_) => const NewFeatureScreen());
```

### 4. Navigate to Route
```dart
AppRouter.navigateTo(context, AppRoutes.newFeature);
```

## Dependency Injection Integration

All screens use GetIt for dependency injection:

```dart
// In screen widgets
final authCubit = getIt<AuthCubit>();
final marketRepository = getIt<MarketRepository>();
```

## Error Handling

The navigation system includes error handling:
- Invalid routes show "Page Not Found" screen
- Navigation errors are caught and handled gracefully
- Context safety checks prevent navigation after widget disposal

## Next Implementation Steps

### Priority 1: Core Functionality
1. **Complete Login/Register Forms**
   - Add form validation
   - Connect to AuthCubit
   - Handle loading states

2. **Implement Market Data**
   - Connect to MarketRepository
   - Add real-time WebSocket updates
   - Create stock list and detail views

3. **Wallet Integration**
   - Connect to WalletRepository
   - Implement balance display
   - Add transfer functionality

### Priority 2: Enhanced Features
1. **Profile Management**
   - User info editing
   - Security settings
   - Verification processes

2. **Product Management**
   - Product listings
   - Purchase flow
   - Order management

3. **Settings & Preferences**
   - Theme switching
   - Language selection
   - Notification settings

The navigation system is now fully functional and ready for feature implementation!
