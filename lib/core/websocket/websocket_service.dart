import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../storage/secure_storage_helper.dart';
import '../constants/local_storage_keys.dart';

@singleton
class WebSocketService {
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>>? _messageController;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const Duration heartbeatInterval = Duration(seconds: 30);

  final SecureStorageHelper _secureStorage;

  WebSocketService(this._secureStorage);

  /// Get message stream
  Stream<Map<String, dynamic>> get messageStream {
    _messageController ??= StreamController<Map<String, dynamic>>.broadcast();
    return _messageController!.stream;
  }

  /// Check if WebSocket is connected
  bool get isConnected => _isConnected;

  /// Connect to WebSocket
  Future<void> connect(String url) async {
    if (_isConnected) {
      log('WebSocket already connected', name: 'WebSocketService');
      return;
    }

    try {
      log('Connecting to WebSocket: $url', name: 'WebSocketService');
      
      // Get auth token for authenticated connection
      final token = await _secureStorage.readSecureData(LocalStorageKeys.token);
      
      // Add token to URL if available
      final wsUrl = token != null ? '$url?token=$token' : url;
      
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _reconnectAttempts = 0;
      _startHeartbeat();
      
      log('WebSocket connected successfully', name: 'WebSocketService');
    } catch (e) {
      log('WebSocket connection error: $e', name: 'WebSocketService');
      _scheduleReconnect();
    }
  }

  /// Disconnect from WebSocket
  Future<void> disconnect() async {
    _shouldReconnect = false;
    _stopHeartbeat();
    _stopReconnectTimer();
    
    if (_channel != null) {
      await _channel!.sink.close(status.normalClosure);
      _channel = null;
    }
    
    _isConnected = false;
    log('WebSocket disconnected', name: 'WebSocketService');
  }

  /// Send message through WebSocket
  void sendMessage(Map<String, dynamic> message) {
    if (_isConnected && _channel != null) {
      final jsonMessage = jsonEncode(message);
      _channel!.sink.add(jsonMessage);
      log('Message sent: $jsonMessage', name: 'WebSocketService');
    } else {
      log('Cannot send message: WebSocket not connected', name: 'WebSocketService');
    }
  }

  /// Subscribe to specific message type
  Stream<Map<String, dynamic>> onMessage(String type) {
    return messageStream.where((message) => message['type'] == type);
  }

  void _onMessage(dynamic data) {
    try {
      final Map<String, dynamic> message = jsonDecode(data);
      log('Message received: $message', name: 'WebSocketService');
      
      _messageController?.add(message);
      
      // Handle ping/pong for heartbeat
      if (message['type'] == 'ping') {
        sendMessage({'type': 'pong'});
      }
    } catch (e) {
      log('Error parsing WebSocket message: $e', name: 'WebSocketService');
    }
  }

  void _onError(error) {
    log('WebSocket error: $error', name: 'WebSocketService');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _onDisconnected() {
    log('WebSocket disconnected', name: 'WebSocketService');
    _isConnected = false;
    _stopHeartbeat();
    
    if (_shouldReconnect) {
      _scheduleReconnect();
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (_isConnected) {
        sendMessage({'type': 'ping', 'timestamp': DateTime.now().millisecondsSinceEpoch});
      }
    });
  }

  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void _scheduleReconnect() {
    if (!_shouldReconnect || _reconnectAttempts >= maxReconnectAttempts) {
      log('Max reconnect attempts reached or reconnect disabled', name: 'WebSocketService');
      return;
    }

    _stopReconnectTimer();
    _reconnectAttempts++;
    
    log('Scheduling reconnect attempt $_reconnectAttempts in ${reconnectDelay.inSeconds}s', 
        name: 'WebSocketService');
    
    _reconnectTimer = Timer(reconnectDelay, () async {
      final wsUrl = await _secureStorage.readSecureData(LocalStorageKeys.webSocketUrl);
      if (wsUrl != null) {
        await connect(wsUrl);
      }
    });
  }

  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Save WebSocket URL for reconnection
  Future<void> saveWebSocketUrl(String url) async {
    await _secureStorage.writeSecureData(LocalStorageKeys.webSocketUrl, url);
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController?.close();
    _messageController = null;
  }
}
