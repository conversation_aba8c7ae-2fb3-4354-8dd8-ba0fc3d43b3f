import 'dart:async';
import '../di/injection.dart';
import 'websocket_service.dart';

mixin WebSocketMixin {
  WebSocketService get _webSocketService => getIt<WebSocketService>();
  
  /// Subscribe to WebSocket messages of a specific type
  Stream<Map<String, dynamic>> onMessage(String type, {bool loginRequired = false}) {
    if (loginRequired && !_webSocketService.isConnected) {
      // TODO: Check if user is logged in and connect if needed
    }
    
    return _webSocketService.onMessage(type);
  }
  
  /// Send a WebSocket message
  void sendWebSocketMessage(Map<String, dynamic> message) {
    _webSocketService.sendMessage(message);
  }
  
  /// Check if WebSocket is connected
  bool get isWebSocketConnected => _webSocketService.isConnected;
  
  /// Connect to WebSocket
  Future<void> connectWebSocket(String url) async {
    await _webSocketService.connect(url);
  }
  
  /// Disconnect from WebSocket
  Future<void> disconnectWebSocket() async {
    await _webSocketService.disconnect();
  }
}
