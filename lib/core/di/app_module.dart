import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@module
abstract class AppModule {
  // NetworkProvider, SecureStorageHelper, and WebSocketService are already registered
  // with @singleton annotations in their respective classes

  @preResolve
  @singleton
  Future<SharedPreferences> get sharedPreferences => SharedPreferences.getInstance();
}
