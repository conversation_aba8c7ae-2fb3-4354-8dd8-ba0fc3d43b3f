import 'package:freezed_annotation/freezed_annotation.dart';

part 'response_result.freezed.dart';

/// Generic wrapper class that encapsulates API responses
@freezed
class ResponseResult<T> with _$ResponseResult<T> {
  const factory ResponseResult({
    T? data,
    String? error,
    String? token,
    @Default(false) bool isLoading,
  }) = _ResponseResult<T>;

  const ResponseResult._();

  /// Returns true if the response contains data and no error
  bool get isSuccess => data != null && error == null;

  /// Returns true if the response contains an error
  bool get isError => error != null;

  /// Creates a successful response with data
  factory ResponseResult.success(T data, {String? token}) {
    return ResponseResult(data: data, token: token);
  }

  /// Creates an error response
  factory ResponseResult.error(String error) {
    return ResponseResult(error: error);
  }

  /// Creates a loading response
  factory ResponseResult.loading() {
    return const ResponseResult(isLoading: true);
  }

  /// Maps the data to a new type while preserving error state
  ResponseResult<R> map<R>(R Function(T data) mapper) {
    final currentData = data;
    if (isSuccess && currentData != null) {
      try {
        return ResponseResult.success(mapper(currentData), token: token);
      } catch (e) {
        return ResponseResult.error('Mapping error: $e');
      }
    }
    final currentError = error;
    if (isError && currentError != null) {
      return ResponseResult.error(currentError);
    }
    if (isLoading) {
      return ResponseResult.loading();
    }
    return ResponseResult.error('Unknown state');
  }

  /// Executes a callback when the response is successful
  ResponseResult<T> onSuccess(void Function(T data) callback) {
    final currentData = data;
    if (isSuccess && currentData != null) {
      callback(currentData);
    }
    return this;
  }

  /// Executes a callback when the response has an error
  ResponseResult<T> onError(void Function(String error) callback) {
    final currentError = error;
    if (isError && currentError != null) {
      callback(currentError);
    }
    return this;
  }
}
