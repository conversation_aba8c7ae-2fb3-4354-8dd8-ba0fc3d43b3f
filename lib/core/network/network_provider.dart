import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../storage/secure_storage_helper.dart';
import '../constants/local_storage_keys.dart';
import '../../config/app_config.dart';

@singleton
class NetworkProvider {
  late final Dio _dio;

  NetworkProvider() {
    _initialize();
  }

  void _initialize({String? baseUrl}) {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl ?? AppConfig.fullApiUrl,
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        connectTimeout: AppConfig.connectTimeout,
        receiveTimeout: AppConfig.receiveTimeout,
        sendTimeout: AppConfig.sendTimeout,
      ),
    );
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: _handleRequest,
        onResponse: _handleResponse,
        onError: _handleError,
      ),
    );

    // Add logging interceptor in debug mode
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => log(obj.toString(), name: 'NetworkProvider'),
      ),
    );
  }

  Future<void> _handleRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Handle authentication
    if (options.headers.containsKey('auth')) {
      options.headers.remove('auth');

      final String? token = await SecureStorageHelper().readSecureData(
        LocalStorageKeys.token,
      );

      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = token;
      }
    }

    handler.next(options);
  }

  void _handleResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    // Log successful responses
    log('Response: ${response.statusCode} - ${response.requestOptions.path}',
        name: 'NetworkProvider');
    handler.next(response);
  }

  Future<void> _handleError(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    log('Error: ${error.response?.statusCode} - ${error.requestOptions.path}',
        name: 'NetworkProvider');

    // Handle 401 Unauthorized
    if (error.response?.statusCode == 401) {
      await _handleUnauthorizedError();
    }

    handler.next(error);
  }

  Future<void> _handleUnauthorizedError() async {
    // Clear stored token
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.token);
    // TODO: Navigate to login screen
    log('Unauthorized access - token cleared', name: 'NetworkProvider');
  }

  /// GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool isAuthRequired = false,
  }) async {
    final requestOptions = options ?? Options();
    if (isAuthRequired) {
      requestOptions.headers = {...?requestOptions.headers, 'auth': true};
    }

    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: requestOptions,
    );
  }

  /// POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool isAuthRequired = false,
  }) async {
    final requestOptions = options ?? Options();
    if (isAuthRequired) {
      requestOptions.headers = {...?requestOptions.headers, 'auth': true};
    }

    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: requestOptions,
    );
  }

  /// PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool isAuthRequired = false,
  }) async {
    final requestOptions = options ?? Options();
    if (isAuthRequired) {
      requestOptions.headers = {...?requestOptions.headers, 'auth': true};
    }

    return await _dio.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: requestOptions,
    );
  }

  /// DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool isAuthRequired = false,
  }) async {
    final requestOptions = options ?? Options();
    if (isAuthRequired) {
      requestOptions.headers = {...?requestOptions.headers, 'auth': true};
    }

    return await _dio.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: requestOptions,
    );
  }

  /// Form data request (for file uploads)
  Future<Response<T>> formData<T>(
    String path, {
    required FormData data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    bool isAuthRequired = false,
    ProgressCallback? onSendProgress,
  }) async {
    final requestOptions = options ?? Options();
    if (isAuthRequired) {
      requestOptions.headers = {...?requestOptions.headers, 'auth': true};
    }

    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: requestOptions,
      onSendProgress: onSendProgress,
    );
  }
}
