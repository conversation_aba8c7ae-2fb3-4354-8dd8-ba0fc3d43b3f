import 'dart:developer';
import 'package:dio/dio.dart';

class <PERSON><PERSON>r<PERSON><PERSON>ler {
  /// Handle Dio exceptions and return user-friendly error messages
  static String handleDioError(DioException e) {
    log('DioException: ${e.type} - ${e.message}', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>');
    
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Server response timeout. Please try again.';
      case DioExceptionType.badResponse:
        return _handleBadResponse(e.response);
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      case DioExceptionType.badCertificate:
        return 'Certificate error. Please contact support.';
      case DioExceptionType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
  
  static String _handleBadResponse(Response? response) {
    if (response == null) {
      return 'Server error. Please try again.';
    }
    
    final statusCode = response.statusCode;
    final data = response.data;
    
    // Try to extract error message from response
    String? errorMessage;
    if (data is Map<String, dynamic>) {
      errorMessage = data['msg'] ?? data['message'] ?? data['error'];
    }
    
    switch (statusCode) {
      case 400:
        return errorMessage ?? 'Bad request. Please check your input.';
      case 401:
        return errorMessage ?? 'Authentication failed. Please login again.';
      case 403:
        return errorMessage ?? 'Access denied. You don\'t have permission.';
      case 404:
        return errorMessage ?? 'Resource not found.';
      case 422:
        return errorMessage ?? 'Validation error. Please check your input.';
      case 429:
        return errorMessage ?? 'Too many requests. Please try again later.';
      case 500:
        return errorMessage ?? 'Internal server error. Please try again later.';
      case 502:
        return errorMessage ?? 'Bad gateway. Please try again later.';
      case 503:
        return errorMessage ?? 'Service unavailable. Please try again later.';
      case 504:
        return errorMessage ?? 'Gateway timeout. Please try again later.';
      default:
        return errorMessage ?? 'Server error ($statusCode). Please try again.';
    }
  }
  
  /// Handle general exceptions
  static String handleGeneralError(dynamic error) {
    log('General error: $error', name: 'ErrorHandler');
    
    if (error is DioException) {
      return handleDioError(error);
    }
    
    if (error is FormatException) {
      return 'Data format error. Please try again.';
    }
    
    if (error is TypeError) {
      return 'Data type error. Please contact support.';
    }
    
    return 'An unexpected error occurred: ${error.toString()}';
  }
  
  /// Log error for debugging
  static void logError(dynamic error, {String? context, StackTrace? stackTrace}) {
    final contextInfo = context != null ? '[$context] ' : '';
    log('${contextInfo}Error: $error', name: 'ErrorHandler');
    
    if (stackTrace != null) {
      log('StackTrace: $stackTrace', name: 'ErrorHandler');
    }
  }
}
