import '../../config/app_config.dart';

class HostUtil {
  static HostUtil? _instance;
  static HostUtil get instance => _instance ??= HostUtil._();

  HostUtil._();

  String? _currentHost;

  /// Get current host
  String? get currentHost => _currentHost;

  /// Set current host
  void setHost(String host) {
    _currentHost = host;
  }

  /// Get default host
  String get defaultHost => AppConfig.baseUrl;

  /// Reset to default host
  void resetToDefault() {
    _currentHost = null;
  }

  /// Get full API URL
  String get fullApiUrl {
    final host = _currentHost ?? defaultHost;
    return '$host${AppConfig.apiPath}';
  }
}
