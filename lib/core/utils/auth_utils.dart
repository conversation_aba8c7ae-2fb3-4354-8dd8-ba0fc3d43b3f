import '../di/injection.dart';
import '../storage/secure_storage_helper.dart';
import '../constants/local_storage_keys.dart';

class AuthUtils {
  static AuthUtils? _instance;
  static AuthUtils get instance => _instance ??= AuthUtils._();
  
  AuthUtils._();
  
  SecureStorageHelper get _secureStorage => getIt<SecureStorageHelper>();
  
  /// Check if user is signed in
  Future<bool> get isSignedIn async {
    final token = await _secureStorage.readSecureData(LocalStorageKeys.token);
    return token != null && token.isNotEmpty;
  }
  
  /// Get current auth token
  Future<String?> get authToken async {
    return await _secureStorage.readSecureData(LocalStorageKeys.token);
  }
  
  /// Save auth token
  Future<void> saveAuthToken(String token) async {
    await _secureStorage.writeSecureData(LocalStorageKeys.token, token);
  }
  
  /// Clear auth token
  Future<void> clearAuthToken() async {
    await _secureStorage.deleteSecureData(LocalStorageKeys.token);
  }
  
  /// Get user ID
  Future<String?> get userId async {
    return await _secureStorage.readSecureData(LocalStorageKeys.userId);
  }
  
  /// Save user ID
  Future<void> saveUserId(String userId) async {
    await _secureStorage.writeSecureData(LocalStorageKeys.userId, userId);
  }
  
  /// Get user email
  Future<String?> get userEmail async {
    return await _secureStorage.readSecureData(LocalStorageKeys.userEmail);
  }
  
  /// Save user email
  Future<void> saveUserEmail(String email) async {
    await _secureStorage.writeSecureData(LocalStorageKeys.userEmail, email);
  }
  
  /// Clear all user data
  Future<void> clearUserData() async {
    await Future.wait([
      _secureStorage.deleteSecureData(LocalStorageKeys.token),
      _secureStorage.deleteSecureData(LocalStorageKeys.userId),
      _secureStorage.deleteSecureData(LocalStorageKeys.userEmail),
    ]);
  }
}
