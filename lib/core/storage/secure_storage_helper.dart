import 'dart:developer';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';

@singleton
class SecureStorageHelper {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Write secure data to storage
  Future<void> writeSecureData(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
      log('Secure data written for key: $key', name: 'SecureStorage');
    } catch (e) {
      log('Error writing secure data for key $key: $e', name: 'SecureStorage');
      rethrow;
    }
  }

  /// Read secure data from storage
  Future<String?> readSecureData(String key) async {
    try {
      final value = await _storage.read(key: key);
      log('Secure data read for key: $key', name: 'SecureStorage');
      return value;
    } catch (e) {
      log('Error reading secure data for key $key: $e', name: 'SecureStorage');
      return null;
    }
  }

  /// Delete secure data from storage
  Future<void> deleteSecureData(String key) async {
    try {
      await _storage.delete(key: key);
      log('Secure data deleted for key: $key', name: 'SecureStorage');
    } catch (e) {
      log('Error deleting secure data for key $key: $e', name: 'SecureStorage');
      rethrow;
    }
  }

  /// Check if key exists in storage
  Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      log('Error checking key existence for $key: $e', name: 'SecureStorage');
      return false;
    }
  }

  /// Clear all secure data
  Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
      log('All secure data cleared', name: 'SecureStorage');
    } catch (e) {
      log('Error clearing all secure data: $e', name: 'SecureStorage');
      rethrow;
    }
  }

  /// Get all keys
  Future<Map<String, String>> readAll() async {
    try {
      final allData = await _storage.readAll();
      log('All secure data read', name: 'SecureStorage');
      return allData;
    } catch (e) {
      log('Error reading all secure data: $e', name: 'SecureStorage');
      return {};
    }
  }
}
