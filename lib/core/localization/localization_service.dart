import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/local_storage_keys.dart';

@singleton
@injectable
class LocalizationService {
  static const Map<String, String> supportedLanguages = {
    'us': 'English (United States)',
    'kr': '한국어 (대한민국)',
    'br': 'Português (Brasil)',
    'fr': 'Français (France)',
    'in': 'हिन्दी (भारत)',
    'es': 'Español (España)',
    'jp': '日本語 (日本)',
    'cn': '繁体中文',
    'sa': 'العربية (السعودية)',
    'de': 'Deutsch (Deutschland)',
    'my': 'Bahasa Melayu (Malaysia)',
    'mx': 'Español (México)',
  };

  static const Map<String, Locale> localeMap = {
    'us': Locale('en', 'US'),
    'kr': Locale('ko', 'KR'),
    'br': Locale('pt', 'BR'),
    'fr': Locale('fr', 'FR'),
    'in': Locale('hi', 'IN'),
    'es': Locale('es', 'ES'),
    'jp': Locale('ja', 'JP'),
    'cn': Locale('zh', 'TW'),
    'sa': Locale('ar', 'SA'),
    'de': Locale('de', 'DE'),
    'my': Locale('ms', 'MY'),
    'mx': Locale('es', 'MX'),
  };

  static const List<Locale> supportedLocales = [
    Locale('en', 'US'),
    Locale('ko', 'KR'),
    Locale('pt', 'BR'),
    Locale('fr', 'FR'),
    Locale('hi', 'IN'),
    Locale('es', 'ES'),
    Locale('ja', 'JP'),
    Locale('zh', 'TW'),
    Locale('ar', 'SA'),
    Locale('de', 'DE'),
    Locale('ms', 'MY'),
  ];

  final SharedPreferences _prefs;
  Locale _currentLocale = const Locale('en', 'US');

  LocalizationService(this._prefs) {
    _loadSavedLanguage();
  }

  Locale get currentLocale => _currentLocale;

  String get currentLanguageCode {
    return localeMap.entries
        .firstWhere(
          (entry) => entry.value == _currentLocale,
          orElse: () => const MapEntry('us', Locale('en', 'US')),
        )
        .key;
  }

  String get currentLanguageName {
    return supportedLanguages[currentLanguageCode] ?? 'English (United States)';
  }

  Future<void> _loadSavedLanguage() async {
    final savedLanguage = _prefs.getString(LocalStorageKeys.language);
    if (savedLanguage != null && localeMap.containsKey(savedLanguage)) {
      _currentLocale = localeMap[savedLanguage]!;
    } else {
      // Use system locale if available, otherwise default to English
      final systemLocale = PlatformDispatcher.instance.locale;
      _currentLocale = _findBestMatchingLocale(systemLocale);
    }
  }

  Locale _findBestMatchingLocale(Locale systemLocale) {
    // Try to find exact match
    for (final locale in supportedLocales) {
      if (locale.languageCode == systemLocale.languageCode &&
          locale.countryCode == systemLocale.countryCode) {
        return locale;
      }
    }

    // Try to find language match
    for (final locale in supportedLocales) {
      if (locale.languageCode == systemLocale.languageCode) {
        return locale;
      }
    }

    // Default to English
    return const Locale('en', 'US');
  }

  Future<void> setLanguage(String languageCode) async {
    if (localeMap.containsKey(languageCode)) {
      _currentLocale = localeMap[languageCode]!;
      await _prefs.setString(LocalStorageKeys.language, languageCode);
    }
  }

  Future<void> setLocale(Locale locale) async {
    _currentLocale = locale;
    final languageCode = localeMap.entries
        .firstWhere(
          (entry) => entry.value == locale,
          orElse: () => const MapEntry('us', Locale('en', 'US')),
        )
        .key;
    await _prefs.setString(LocalStorageKeys.language, languageCode);
  }

  List<MapEntry<String, String>> getSupportedLanguagesList() {
    return supportedLanguages.entries.toList();
  }

  bool isRTL() {
    return _currentLocale.languageCode == 'ar';
  }

  String getLanguageFlag(String languageCode) {
    const flagMap = {
      'us': '🇺🇸',
      'kr': '🇰🇷',
      'br': '🇧🇷',
      'fr': '🇫🇷',
      'in': '🇮🇳',
      'es': '🇪🇸',
      'jp': '🇯🇵',
      'cn': '🇹🇼',
      'sa': '🇸🇦',
      'de': '🇩🇪',
      'my': '🇲🇾',
      'mx': '🇲🇽',
    };
    return flagMap[languageCode] ?? '🌐';
  }
}
