class AppRoutes {
  // Auth Routes
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // Main App Routes
  static const String home = '/home';
  static const String dashboard = '/dashboard';
  
  // Market Routes
  static const String market = '/market';
  static const String stockDetail = '/market/stock-detail';
  static const String stockChart = '/market/stock-chart';
  static const String marketSearch = '/market/search';
  
  // Wallet Routes
  static const String wallet = '/wallet';
  static const String walletTransfer = '/wallet/transfer';
  static const String walletWithdraw = '/wallet/withdraw';
  static const String walletHistory = '/wallet/history';
  
  // Profile Routes
  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String profileSecurity = '/profile/security';
  static const String profileVerification = '/profile/verification';
  
  // Product Routes
  static const String products = '/products';
  static const String productDetail = '/products/detail';
  static const String productPurchase = '/products/purchase';
  static const String myProducts = '/products/my-products';
  
  // Settings Routes
  static const String settings = '/settings';
  static const String settingsNotifications = '/settings/notifications';
  static const String settingsLanguage = '/settings/language';
  static const String settingsTheme = '/settings/theme';
  
  // Support Routes
  static const String support = '/support';
  static const String supportChat = '/support/chat';
  static const String supportFaq = '/support/faq';
  
  // Legal Routes
  static const String privacy = '/privacy';
  static const String terms = '/terms';
  static const String about = '/about';
  
  // Private constructor to prevent instantiation
  AppRoutes._();
}
