import 'package:flutter/material.dart';
import 'app_routes.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/market/screens/market_screen.dart';
import '../../features/market/screens/stock_detail_screen.dart';
import '../../features/wallet/screens/wallet_screen.dart';
import '../../features/wallet/screens/wallet_transfer_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/profile_edit_screen.dart';
import '../../features/products/screens/products_screen.dart';
import '../../features/settings/screens/settings_screen.dart';

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      // Auth Routes
      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      
      case AppRoutes.login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      
      case AppRoutes.register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());
      
      // Main App Routes
      case AppRoutes.home:
      case AppRoutes.dashboard:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      
      // Market Routes
      case AppRoutes.market:
        return MaterialPageRoute(builder: (_) => const MarketScreen());
      
      case AppRoutes.stockDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final symbol = args?['symbol'] as String?;
        return MaterialPageRoute(
          builder: (_) => StockDetailScreen(symbol: symbol ?? ''),
        );
      
      // Wallet Routes
      case AppRoutes.wallet:
        return MaterialPageRoute(builder: (_) => const WalletScreen());
      
      case AppRoutes.walletTransfer:
        return MaterialPageRoute(builder: (_) => const WalletTransferScreen());
      
      // Profile Routes
      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      
      case AppRoutes.profileEdit:
        return MaterialPageRoute(builder: (_) => const ProfileEditScreen());
      
      // Product Routes
      case AppRoutes.products:
        return MaterialPageRoute(builder: (_) => const ProductsScreen());
      
      // Settings Routes
      case AppRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());
      
      // Default route
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Text(
                'Page not found',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ),
        );
    }
  }
  
  // Navigation helper methods
  static void navigateTo(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushNamed(context, routeName, arguments: arguments);
  }
  
  static void navigateToAndClearStack(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }
  
  static void navigateToAndReplace(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
  }
  
  static void goBack(BuildContext context) {
    Navigator.pop(context);
  }
  
  static bool canGoBack(BuildContext context) {
    return Navigator.canPop(context);
  }
}
