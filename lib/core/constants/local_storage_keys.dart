class LocalStorageKeys {
  // Authentication
  static const String token = 'auth_token';
  static const String refreshToken = 'refresh_token';
  static const String userId = 'user_id';
  static const String userEmail = 'user_email';
  
  // User Preferences
  static const String themeMode = 'theme_mode';
  static const String language = 'language';
  static const String isFirstLaunch = 'is_first_launch';
  
  // App Settings
  static const String notificationsEnabled = 'notifications_enabled';
  static const String biometricEnabled = 'biometric_enabled';
  
  // Cache
  static const String lastSyncTime = 'last_sync_time';
  static const String cachedUserProfile = 'cached_user_profile';
  
  // WebSocket
  static const String webSocketUrl = 'websocket_url';
  
  // Private constructor to prevent instantiation
  LocalStorageKeys._();
}
