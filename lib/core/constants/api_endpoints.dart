class ApiEndpoints {
  // Authentication & Registration
  static const String login = '/login';
  static const String logout = '/logout';
  static const String register = '/register/';
  static const String registerCaptcha = '/register/captcha';
  static const String registerOtpSend = '/register/otp/send';
  static const String registerCheckCode = '/register/checkCode';
  
  // Password Management
  static const String changePassword = '/change_password';
  static const String changePasswordEmailCaptchaSend = '/change_password/email_captcha/send';
  static const String changePasswordEmailCaptchaCheck = '/change_password/email_captcha/check';
  
  // Login Utilities
  static const String loginCaptcha = '/login/captcha';
  static const String loginIpDetail = '/login/getIpDetail';
  
  // User Authentication
  static const String authGoogleToken = '/auth/google_token';
  static const String authGoogleTokenCheck = '/auth/google_token/check';
  static const String authIdentity = '/auth/identity';
  static const String authMobileNo = '/auth/mobile_no';
  static const String authPayPassword = '/auth/pay_password';
  static const String authStatus = '/auth/status';
  
  // User Profile
  static const String profileInfo = '/profile/info';
  static const String profileEmailCaptchaSend = '/profile/email_captcha/send';
  static const String profileEmailCaptchaCheck = '/profile/email_captcha/check';
  static const String profileExpendAddress = '/profile/expend_address';
  static const String profileExpendAddressSave = '/profile/expend_address/save';
  static const String profileLoginPasswordChange = '/profile/login_password/change';
  static const String profilePayPasswordChange = '/profile/pay_password/change';
  static const String profileResetGoogleToken = '/profile/reset_google_token';
  static const String profileResetGoogleTokenCheck = '/profile/reset_google_token/check';
  
  // Announcements
  static const String announcementInfo = '/announcement/info';
  static const String announcementHaveRead = '/announcement/have_read';
  
  // Articles
  static const String articleDetails = '/article/details';
  static const String articlePage = '/article/page';
  
  // Carousel
  static String carouselList(String type) => '/carousel/$type/list';
  
  // Company Configuration
  static const String configAboutUs = '/config/about_us';
  static const String configOnlineService = '/config/online_service';
  static const String configPrivacy = '/config/privacy';
  static const String configProtocol = '/config/protocol';
  static const String configServiceAgreement = '/config/serviceAgreement';
  
  // Customer Service
  static const String customerServiceChannelList = '/customer-service-channel/list';
  
  // Community
  static const String communityFriendPage = '/community/friend/page';
  
  // Market Data
  static const String marketDepthL2 = '/market/depth/l2';
  static const String marketComponentStock = '/market/getComponentStock';
  static const String marketGainDistribution = '/market/getGainDistribution';
  static const String marketPlate = '/market/getMarketPlate';
  static const String marketPlateList = '/market/getPlateList';
  static const String marketStockKline = '/market/getStockKline';
  static const String marketStockList = '/market/getStockList';
  static const String marketStockListByKeyword = '/market/getStockListByKeyword';
  static const String marketKline = '/market/kline';
  static const String marketSecurityList = '/market/security/list';
  static const String marketSecurityListData = '/market/securityList';
  static const String marketStockInfo = '/market/stockInfo';
  static const String marketTimeLine = '/market/timeLine';
  static const String marketTimeLineMin = '/market/timeLine/min';
  
  // Mentor
  static const String mentorInfoPage = '/mentor/info/page';
  
  // Products
  static const String productIncreaseContract = '/product/increase_contract';
  static String productList(int mentorId) => '/product/list/$mentorId';
  static const String productOrderDetail = '/product/oderDetail';
  static const String productOrderPage = '/product/oderPage';
  static const String productOrderRejectPage = '/product/oderRejectPage';
  static const String productPay = '/product/pay';
  static const String productProposeProfit = '/product/proposeProfit';
  static const String productPurchased = '/product/purchased';
  static String productTransactionRecord(int id) => '/product/transaction/$id/record';
  static const String productUnbinding = '/product/unbinding';
  
  // Stock Transactions
  static const String stockTransactionPage = '/stock/transaction/page';
  
  // Tencent IM
  static const String tcAutoAddKefu = '/tc/autoAddKefu';
  static const String tcCheckFriendAssignment = '/tc/checkFriendAssignment';
  static const String tcDeleteFriend = '/tc/deleteFriend';
  static const String tcFriends = '/tc/friends';
  static const String tcGetProfile = '/tc/getProfile';
  static const String tcUpdateProfile = '/tc/updateProfile';
  static const String tcUserSig = '/tc/userSig';
  
  // File Upload
  static String upload(String fileType) => '/upload/$fileType';
  
  // Wallet
  static const String walletBalance = '/wallet/balance';
  static String walletBalanceByType(String type) => '/wallet/balance/$type';
  static const String walletCollectionAmount = '/wallet/collection/amount';
  static const String walletCollectionSupervisor = '/wallet/collection/supervisor';
  static const String walletCollectionTransfer = '/wallet/collection/transfer';
  static const String walletCollectionTransferUsername = '/wallet/collection/transfer/username';
  static const String walletCollectionWithdraw = '/wallet/collection/withdraw';
  
  // Test
  static const String test = '/test/test';
  
  // Private constructor to prevent instantiation
  ApiEndpoints._();
}
