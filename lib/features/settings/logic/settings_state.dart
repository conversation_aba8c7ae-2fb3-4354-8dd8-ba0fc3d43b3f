import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/repository/settings_repository.dart';

part 'settings_state.freezed.dart';

@freezed
class SettingsState with _$SettingsState {
  const factory SettingsState({
    @Default(false) bool isLoading,
    @Default(null) String? error,
    @Default(null) AppSettings? appSettings,
    @Default(null) NotificationSettings? notificationSettings,
    @Default(null) SecuritySettings? securitySettings,
    @Default(null) PrivacySettings? privacySettings,
    @Default(null) VersionInfo? versionInfo,
    @Default(null) UpdateInfo? updateInfo,
  }) = _SettingsState;
}
