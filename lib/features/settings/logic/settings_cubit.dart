import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/settings_repository.dart';
import 'settings_state.dart';

@injectable
class SettingsCubit extends Cubit<SettingsState> {
  final SettingsRepository _settingsRepository;

  SettingsCubit(this._settingsRepository) : super(const SettingsState());

  Future<void> loadAppSettings() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.getAppSettings();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        appSettings: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> updateAppSettings(AppSettings settings) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.updateAppSettings(settings);

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        appSettings: settings,
        error: null,
      ));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> loadNotificationSettings() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.getNotificationSettings();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        notificationSettings: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> updateNotificationSettings(NotificationSettings settings) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.updateNotificationSettings(settings);

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        notificationSettings: settings,
        error: null,
      ));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> loadSecuritySettings() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.getSecuritySettings();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        securitySettings: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> updateSecuritySettings(SecuritySettings settings) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.updateSecuritySettings(settings);

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        securitySettings: settings,
        error: null,
      ));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> loadPrivacySettings() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.getPrivacySettings();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        privacySettings: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> updatePrivacySettings(PrivacySettings settings) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.updatePrivacySettings(settings);

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        privacySettings: settings,
        error: null,
      ));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<bool> clearCache() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.clearCache();

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> loadVersionInfo() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.getVersionInfo();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        versionInfo: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<void> checkForUpdates() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _settingsRepository.checkForUpdates();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        updateInfo: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }

  void clearUpdateInfo() {
    emit(state.copyWith(updateInfo: null));
  }
}
