import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/settings_repository.dart';

@Injectable(as: SettingsRepository)
class SettingsService implements SettingsRepository {
  final NetworkProvider _networkProvider;
  final SharedPreferences _prefs;

  SettingsService(this._networkProvider, this._prefs);

  @override
  Future<ResponseResult<AppSettings>> getAppSettings() async {
    try {
      // For app settings, we primarily use local storage
      final settings = AppSettings(
        language: _prefs.getString('app_language') ?? 'en',
        theme: _prefs.getString('app_theme') ?? 'system',
        currency: _prefs.getString('app_currency') ?? 'USD',
        biometricEnabled: _prefs.getBool('biometric_enabled') ?? false,
        autoLockEnabled: _prefs.getBool('auto_lock_enabled') ?? false,
        autoLockTimeout: _prefs.getInt('auto_lock_timeout') ?? 300,
        soundEnabled: _prefs.getBool('sound_enabled') ?? true,
        vibrationEnabled: _prefs.getBool('vibration_enabled') ?? true,
      );

      return ResponseResult(data: settings);
    } catch (e) {
      return ResponseResult(error: 'Failed to get app settings: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> updateAppSettings(AppSettings settings) async {
    try {
      // Save to local storage
      if (settings.language != null) {
        await _prefs.setString('app_language', settings.language!);
      }
      if (settings.theme != null) {
        await _prefs.setString('app_theme', settings.theme!);
      }
      if (settings.currency != null) {
        await _prefs.setString('app_currency', settings.currency!);
      }
      if (settings.biometricEnabled != null) {
        await _prefs.setBool('biometric_enabled', settings.biometricEnabled!);
      }
      if (settings.autoLockEnabled != null) {
        await _prefs.setBool('auto_lock_enabled', settings.autoLockEnabled!);
      }
      if (settings.autoLockTimeout != null) {
        await _prefs.setInt('auto_lock_timeout', settings.autoLockTimeout!);
      }
      if (settings.soundEnabled != null) {
        await _prefs.setBool('sound_enabled', settings.soundEnabled!);
      }
      if (settings.vibrationEnabled != null) {
        await _prefs.setBool('vibration_enabled', settings.vibrationEnabled!);
      }

      // Also sync with server if needed
      try {
        await _networkProvider.post(
          '/user/settings/app',
          data: settings.toJson(),
          isAuthRequired: true,
        );
      } catch (e) {
        // Server sync failed, but local save succeeded
        // This is acceptable for app settings
      }

      return ResponseResult(data: true);
    } catch (e) {
      return ResponseResult(error: 'Failed to update app settings: $e');
    }
  }

  @override
  Future<ResponseResult<NotificationSettings>> getNotificationSettings() async {
    try {
      final Response response = await _networkProvider.get(
        '/user/settings/notifications',
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: NotificationSettings.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get notification settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> updateNotificationSettings(NotificationSettings settings) async {
    try {
      final Response response = await _networkProvider.post(
        '/user/settings/notifications',
        data: settings.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to update notification settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<SecuritySettings>> getSecuritySettings() async {
    try {
      final Response response = await _networkProvider.get(
        '/user/settings/security',
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: SecuritySettings.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get security settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> updateSecuritySettings(SecuritySettings settings) async {
    try {
      final Response response = await _networkProvider.post(
        '/user/settings/security',
        data: settings.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to update security settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<PrivacySettings>> getPrivacySettings() async {
    try {
      final Response response = await _networkProvider.get(
        '/user/settings/privacy',
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: PrivacySettings.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get privacy settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> updatePrivacySettings(PrivacySettings settings) async {
    try {
      final Response response = await _networkProvider.post(
        '/user/settings/privacy',
        data: settings.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to update privacy settings');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> clearCache() async {
    try {
      // Clear local cache
      await _prefs.clear();
      
      // You can also clear other caches here like image cache, etc.
      
      return ResponseResult(data: true);
    } catch (e) {
      return ResponseResult(error: 'Failed to clear cache: $e');
    }
  }

  @override
  Future<ResponseResult<VersionInfo>> getVersionInfo() async {
    try {
      // This would typically come from package_info_plus or similar
      final versionInfo = VersionInfo(
        currentVersion: '1.0.0',
        buildNumber: '1',
        releaseDate: DateTime.now().toIso8601String(),
        platform: 'mobile',
      );

      return ResponseResult(data: versionInfo);
    } catch (e) {
      return ResponseResult(error: 'Failed to get version info: $e');
    }
  }

  @override
  Future<ResponseResult<UpdateInfo>> checkForUpdates() async {
    try {
      final Response response = await _networkProvider.get(
        '/app/version/check',
        isAuthRequired: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: UpdateInfo.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check for updates');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }
}
