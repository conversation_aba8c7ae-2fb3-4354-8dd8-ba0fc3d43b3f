import '../../../../core/models/response_result.dart';

abstract class SettingsRepository {
  /// Get app settings
  Future<ResponseResult<AppSettings>> getAppSettings();
  
  /// Update app settings
  Future<ResponseResult<bool>> updateAppSettings(AppSettings settings);
  
  /// Get notification settings
  Future<ResponseResult<NotificationSettings>> getNotificationSettings();
  
  /// Update notification settings
  Future<ResponseResult<bool>> updateNotificationSettings(NotificationSettings settings);
  
  /// Get security settings
  Future<ResponseResult<SecuritySettings>> getSecuritySettings();
  
  /// Update security settings
  Future<ResponseResult<bool>> updateSecuritySettings(SecuritySettings settings);
  
  /// Get privacy settings
  Future<ResponseResult<PrivacySettings>> getPrivacySettings();
  
  /// Update privacy settings
  Future<ResponseResult<bool>> updatePrivacySettings(PrivacySettings settings);
  
  /// Clear app cache
  Future<ResponseResult<bool>> clearCache();
  
  /// Get app version info
  Future<ResponseResult<VersionInfo>> getVersionInfo();
  
  /// Check for app updates
  Future<ResponseResult<UpdateInfo>> checkForUpdates();
}

class AppSettings {
  final String? language;
  final String? theme;
  final String? currency;
  final bool? biometricEnabled;
  final bool? autoLockEnabled;
  final int? autoLockTimeout;
  final bool? soundEnabled;
  final bool? vibrationEnabled;

  AppSettings({
    this.language,
    this.theme,
    this.currency,
    this.biometricEnabled,
    this.autoLockEnabled,
    this.autoLockTimeout,
    this.soundEnabled,
    this.vibrationEnabled,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      language: json['language'],
      theme: json['theme'],
      currency: json['currency'],
      biometricEnabled: json['biometricEnabled'],
      autoLockEnabled: json['autoLockEnabled'],
      autoLockTimeout: json['autoLockTimeout'],
      soundEnabled: json['soundEnabled'],
      vibrationEnabled: json['vibrationEnabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'theme': theme,
      'currency': currency,
      'biometricEnabled': biometricEnabled,
      'autoLockEnabled': autoLockEnabled,
      'autoLockTimeout': autoLockTimeout,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
    };
  }

  AppSettings copyWith({
    String? language,
    String? theme,
    String? currency,
    bool? biometricEnabled,
    bool? autoLockEnabled,
    int? autoLockTimeout,
    bool? soundEnabled,
    bool? vibrationEnabled,
  }) {
    return AppSettings(
      language: language ?? this.language,
      theme: theme ?? this.theme,
      currency: currency ?? this.currency,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoLockEnabled: autoLockEnabled ?? this.autoLockEnabled,
      autoLockTimeout: autoLockTimeout ?? this.autoLockTimeout,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
    );
  }
}

class NotificationSettings {
  final bool? pushEnabled;
  final bool? emailEnabled;
  final bool? smsEnabled;
  final bool? marketUpdates;
  final bool? priceAlerts;
  final bool? newsUpdates;
  final bool? systemNotifications;

  NotificationSettings({
    this.pushEnabled,
    this.emailEnabled,
    this.smsEnabled,
    this.marketUpdates,
    this.priceAlerts,
    this.newsUpdates,
    this.systemNotifications,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      pushEnabled: json['pushEnabled'],
      emailEnabled: json['emailEnabled'],
      smsEnabled: json['smsEnabled'],
      marketUpdates: json['marketUpdates'],
      priceAlerts: json['priceAlerts'],
      newsUpdates: json['newsUpdates'],
      systemNotifications: json['systemNotifications'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pushEnabled': pushEnabled,
      'emailEnabled': emailEnabled,
      'smsEnabled': smsEnabled,
      'marketUpdates': marketUpdates,
      'priceAlerts': priceAlerts,
      'newsUpdates': newsUpdates,
      'systemNotifications': systemNotifications,
    };
  }
}

class SecuritySettings {
  final bool? twoFactorEnabled;
  final bool? biometricLoginEnabled;
  final bool? autoLogoutEnabled;
  final int? autoLogoutTimeout;
  final bool? deviceTrustEnabled;
  final List<String>? trustedDevices;

  SecuritySettings({
    this.twoFactorEnabled,
    this.biometricLoginEnabled,
    this.autoLogoutEnabled,
    this.autoLogoutTimeout,
    this.deviceTrustEnabled,
    this.trustedDevices,
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      twoFactorEnabled: json['twoFactorEnabled'],
      biometricLoginEnabled: json['biometricLoginEnabled'],
      autoLogoutEnabled: json['autoLogoutEnabled'],
      autoLogoutTimeout: json['autoLogoutTimeout'],
      deviceTrustEnabled: json['deviceTrustEnabled'],
      trustedDevices: json['trustedDevices']?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'twoFactorEnabled': twoFactorEnabled,
      'biometricLoginEnabled': biometricLoginEnabled,
      'autoLogoutEnabled': autoLogoutEnabled,
      'autoLogoutTimeout': autoLogoutTimeout,
      'deviceTrustEnabled': deviceTrustEnabled,
      'trustedDevices': trustedDevices,
    };
  }
}

class PrivacySettings {
  final bool? dataCollectionEnabled;
  final bool? analyticsEnabled;
  final bool? crashReportingEnabled;
  final bool? personalizedAdsEnabled;
  final bool? locationTrackingEnabled;

  PrivacySettings({
    this.dataCollectionEnabled,
    this.analyticsEnabled,
    this.crashReportingEnabled,
    this.personalizedAdsEnabled,
    this.locationTrackingEnabled,
  });

  factory PrivacySettings.fromJson(Map<String, dynamic> json) {
    return PrivacySettings(
      dataCollectionEnabled: json['dataCollectionEnabled'],
      analyticsEnabled: json['analyticsEnabled'],
      crashReportingEnabled: json['crashReportingEnabled'],
      personalizedAdsEnabled: json['personalizedAdsEnabled'],
      locationTrackingEnabled: json['locationTrackingEnabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dataCollectionEnabled': dataCollectionEnabled,
      'analyticsEnabled': analyticsEnabled,
      'crashReportingEnabled': crashReportingEnabled,
      'personalizedAdsEnabled': personalizedAdsEnabled,
      'locationTrackingEnabled': locationTrackingEnabled,
    };
  }
}

class VersionInfo {
  final String? currentVersion;
  final String? buildNumber;
  final String? releaseDate;
  final String? platform;

  VersionInfo({
    this.currentVersion,
    this.buildNumber,
    this.releaseDate,
    this.platform,
  });

  factory VersionInfo.fromJson(Map<String, dynamic> json) {
    return VersionInfo(
      currentVersion: json['currentVersion'],
      buildNumber: json['buildNumber'],
      releaseDate: json['releaseDate'],
      platform: json['platform'],
    );
  }
}

class UpdateInfo {
  final bool? hasUpdate;
  final String? latestVersion;
  final String? updateUrl;
  final String? releaseNotes;
  final bool? isForced;

  UpdateInfo({
    this.hasUpdate,
    this.latestVersion,
    this.updateUrl,
    this.releaseNotes,
    this.isForced,
  });

  factory UpdateInfo.fromJson(Map<String, dynamic> json) {
    return UpdateInfo(
      hasUpdate: json['hasUpdate'],
      latestVersion: json['latestVersion'],
      updateUrl: json['updateUrl'],
      releaseNotes: json['releaseNotes'],
      isForced: json['isForced'],
    );
  }
}
