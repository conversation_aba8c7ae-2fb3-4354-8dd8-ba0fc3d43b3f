import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../core/navigation/app_routes.dart';
import '../../../core/navigation/app_router.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/market_cubit.dart';
import '../logic/market_state.dart';
import '../widgets/stock_list_widget.dart';
import '../widgets/market_overview_widget.dart';

class MarketScreen extends StatelessWidget {
  const MarketScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return BlocProvider(
      create: (context) => getIt<MarketCubit>()..loadStockList()..getGainDistribution(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.market_title),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => AppRouter.navigateTo(context, AppRoutes.marketSearch),
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => context.read<MarketCubit>().refreshAll(),
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              MarketOverviewWidget(),
              SizedBox(height: 16),
              Expanded(
                child: StockListWidget(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
