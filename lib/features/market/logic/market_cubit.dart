import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/market_repository.dart';
import '../domain/models/stock_info.dart';
import 'market_state.dart';

@injectable
class MarketCubit extends Cubit<MarketState> {
  final MarketRepository _marketRepository;

  MarketCubit(this._marketRepository) : super(const MarketState());

  /// Load stock list with pagination
  Future<void> loadStockList({
    int? pageNum,
    int? pageSize,
    String? order,
    int? sortType,
    bool isRefresh = false,
  }) async {
    if (isRefresh) {
      emit(state.copyWith(
        isStockListLoading: true,
        stockListError: null,
        currentPage: 1,
      ));
    } else {
      emit(state.copyWith(isStockListLoading: true, stockListError: null));
    }

    try {
      final result = await _marketRepository.getStockList(
        pageNum: pageNum ?? (isRefresh ? 1 : state.currentPage),
        pageSize: pageSize ?? state.pageSize,
        order: order,
        sortType: sortType,
      );

      if (result.isSuccess) {
        final newStocks = result.data ?? [];
        final updatedStocks = isRefresh || state.currentPage == 1
            ? newStocks
            : [...state.stockList, ...newStocks];

        emit(state.copyWith(
          isStockListLoading: false,
          stockList: updatedStocks,
          stockListError: null,
          currentPage: (pageNum ?? state.currentPage) + 1,
          hasMoreData: newStocks.length >= (pageSize ?? state.pageSize),
        ));
      } else {
        emit(state.copyWith(
          isStockListLoading: false,
          stockListError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading stock list: $e');
      emit(state.copyWith(
        isStockListLoading: false,
        stockListError: 'Failed to load stock list: $e',
      ));
    }
  }

  /// Search stocks by keyword
  Future<void> searchStocks({
    required String keyword,
    int? pageNum,
    int? pageSize,
  }) async {
    emit(state.copyWith(
      isSearchLoading: true,
      searchError: null,
      searchKeyword: keyword,
    ));

    try {
      final result = await _marketRepository.searchStocks(
        keyword: keyword,
        pageNum: pageNum,
        pageSize: pageSize,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isSearchLoading: false,
          searchResults: result.data ?? [],
          searchError: null,
        ));
      } else {
        emit(state.copyWith(
          isSearchLoading: false,
          searchError: result.error,
        ));
      }
    } catch (e) {
      log('Error searching stocks: $e');
      emit(state.copyWith(
        isSearchLoading: false,
        searchError: 'Failed to search stocks: $e',
      ));
    }
  }

  /// Get stock information by symbol
  Future<void> getStockInfo(String instrument) async {
    emit(state.copyWith(isStockInfoLoading: true, stockInfoError: null));

    try {
      final result = await _marketRepository.getStockInfo(instrument);

      if (result.isSuccess) {
        emit(state.copyWith(
          isStockInfoLoading: false,
          selectedStock: result.data,
          stockInfoError: null,
        ));
      } else {
        emit(state.copyWith(
          isStockInfoLoading: false,
          stockInfoError: result.error,
        ));
      }
    } catch (e) {
      log('Error getting stock info: $e');
      emit(state.copyWith(
        isStockInfoLoading: false,
        stockInfoError: 'Failed to get stock info: $e',
      ));
    }
  }

  /// Get stock K-line data
  Future<void> getStockKline({
    required String symbol,
    required String period,
  }) async {
    emit(state.copyWith(isKlineLoading: true, klineError: null));

    try {
      final result = await _marketRepository.getStockKline(
        symbol: symbol,
        period: period,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isKlineLoading: false,
          klineData: result.data,
          klineError: null,
        ));
      } else {
        emit(state.copyWith(
          isKlineLoading: false,
          klineError: result.error,
        ));
      }
    } catch (e) {
      log('Error getting K-line data: $e');
      emit(state.copyWith(
        isKlineLoading: false,
        klineError: 'Failed to get K-line data: $e',
      ));
    }
  }

  /// Get market depth data
  Future<void> getMarketDepth({
    required String instrument,
    int? depth,
  }) async {
    emit(state.copyWith(isDepthLoading: true, depthError: null));

    try {
      final result = await _marketRepository.getMarketDepth(
        instrument: instrument,
        depth: depth,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isDepthLoading: false,
          depthData: result.data,
          depthError: null,
        ));
      } else {
        emit(state.copyWith(
          isDepthLoading: false,
          depthError: result.error,
        ));
      }
    } catch (e) {
      log('Error getting market depth: $e');
      emit(state.copyWith(
        isDepthLoading: false,
        depthError: 'Failed to get market depth: $e',
      ));
    }
  }

  /// Get timeline data
  Future<void> getTimeline({
    required String instrument,
    required String period,
  }) async {
    emit(state.copyWith(isTimelineLoading: true, timelineError: null));

    try {
      final result = await _marketRepository.getTimeline(
        instrument: instrument,
        period: period,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isTimelineLoading: false,
          timelineData: result.data,
          timelineError: null,
        ));
      } else {
        emit(state.copyWith(
          isTimelineLoading: false,
          timelineError: result.error,
        ));
      }
    } catch (e) {
      log('Error getting timeline data: $e');
      emit(state.copyWith(
        isTimelineLoading: false,
        timelineError: 'Failed to get timeline data: $e',
      ));
    }
  }

  /// Get gain distribution
  Future<void> getGainDistribution() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _marketRepository.getGainDistribution();

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoading: false,
          gainDistribution: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error getting gain distribution: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to get gain distribution: $e',
      ));
    }
  }

  /// Get market plate data
  Future<void> getMarketPlate({
    String? field,
    String? marketType,
    String? order,
    int? page,
    int? pageSize,
    String? plate,
    String? securityType,
  }) async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _marketRepository.getMarketPlate(
        field: field,
        marketType: marketType,
        order: order,
        page: page,
        pageSize: pageSize,
        plate: plate,
        securityType: securityType,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoading: false,
          marketPlateData: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error getting market plate data: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to get market plate data: $e',
      ));
    }
  }

  /// Clear search results
  void clearSearchResults() {
    emit(state.copyWith(
      searchResults: [],
      searchKeyword: null,
      searchError: null,
    ));
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      stockListError: null,
      searchError: null,
      stockInfoError: null,
      klineError: null,
      depthError: null,
      timelineError: null,
    ));
  }

  /// Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      loadStockList(isRefresh: true),
      getGainDistribution(),
    ]);
  }
}
