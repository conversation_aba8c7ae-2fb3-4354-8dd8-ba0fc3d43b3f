import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/stock_info.dart';

part 'market_state.freezed.dart';

@freezed
class MarketState with _$MarketState {
  const factory MarketState({
    @Default(false) bool isLoading,
    @Default(false) bool isStockListLoading,
    @Default(false) bool isSearchLoading,
    @Default(false) bool isStockInfoLoading,
    @Default(false) bool isKlineLoading,
    @Default(false) bool isDepthLoading,
    @Default(false) bool isTimelineLoading,
    @Default([]) List<StockInfo> stockList,
    @Default([]) List<StockInfo> searchResults,
    StockInfo? selectedStock,
    Map<String, dynamic>? klineData,
    Map<String, dynamic>? depthData,
    Map<String, dynamic>? timelineData,
    Map<String, dynamic>? gainDistribution,
    Map<String, dynamic>? marketPlateData,
    String? error,
    String? stockListError,
    String? searchError,
    String? stockInfoError,
    String? klineError,
    String? depthError,
    String? timelineError,
    String? searchKeyword,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(false) bool hasMoreData,
  }) = _MarketState;
}
