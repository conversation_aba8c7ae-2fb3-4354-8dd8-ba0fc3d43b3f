import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/market_cubit.dart';
import '../logic/market_state.dart';
import '../domain/models/stock_info.dart';
import 'stock_item_widget.dart';

class StockListWidget extends StatefulWidget {
  const StockListWidget({super.key});

  @override
  State<StockListWidget> createState() => _StockListWidgetState();
}

class _StockListWidgetState extends State<StockListWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<MarketCubit>().state;
      if (!state.isStockListLoading && state.hasMoreData) {
        context.read<MarketCubit>().loadStockList();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with sort options
            Row(
              children: [
                Text(
                  'Stocks',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.sort),
                  onSelected: (value) => _handleSort(value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'price_asc',
                      child: Text('Price (Low to High)'),
                    ),
                    const PopupMenuItem(
                      value: 'price_desc',
                      child: Text('Price (High to Low)'),
                    ),
                    const PopupMenuItem(
                      value: 'change_asc',
                      child: Text('Change (Low to High)'),
                    ),
                    const PopupMenuItem(
                      value: 'change_desc',
                      child: Text('Change (High to Low)'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Stock list
            Expanded(
              child: _buildStockList(state, l10n),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStockList(MarketState state, AppLocalizations l10n) {
    if (state.isStockListLoading && state.stockList.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.stockListError != null && state.stockList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              state.stockListError!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.read<MarketCubit>().loadStockList(isRefresh: true),
              child: Text(l10n.common_retry),
            ),
          ],
        ),
      );
    }

    if (state.stockList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.trending_up,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No stocks available',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => context.read<MarketCubit>().loadStockList(isRefresh: true),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: state.stockList.length + (state.hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.stockList.length) {
            // Loading indicator for pagination
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          final stock = state.stockList[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: StockItemWidget(
              stock: stock,
              onTap: () => _onStockTap(stock),
            ),
          );
        },
      ),
    );
  }

  void _handleSort(String sortType) {
    String? order;
    int? sortTypeInt;

    switch (sortType) {
      case 'price_asc':
        order = 'ASC';
        sortTypeInt = 1;
        break;
      case 'price_desc':
        order = 'DESC';
        sortTypeInt = 1;
        break;
      case 'change_asc':
        order = 'ASC';
        sortTypeInt = 2;
        break;
      case 'change_desc':
        order = 'DESC';
        sortTypeInt = 2;
        break;
    }

    context.read<MarketCubit>().loadStockList(
      isRefresh: true,
      order: order,
      sortType: sortTypeInt,
    );
  }

  void _onStockTap(StockInfo stock) {
    // Navigate to stock detail screen
    // This would be implemented when we have the stock detail screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Stock detail for ${stock.name ?? stock.symbol} - Coming soon!'),
      ),
    );
  }
}
