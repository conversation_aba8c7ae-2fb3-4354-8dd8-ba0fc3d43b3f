import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/market_cubit.dart';
import '../logic/market_state.dart';

class MarketOverviewWidget extends StatelessWidget {
  const MarketOverviewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: Colors.green,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Market Overview',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                if (state.isLoading)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (state.error != null)
                  Center(
                    child: Column(
                      children: [
                        Text(
                          state.error!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: () => context.read<MarketCubit>().getGainDistribution(),
                          child: Text(l10n.common_retry),
                        ),
                      ],
                    ),
                  )
                else if (state.gainDistribution != null)
                  _buildGainDistribution(state.gainDistribution!)
                else
                  _buildPlaceholderOverview(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGainDistribution(Map<String, dynamic> data) {
    // Extract gain distribution data
    final rising = data['rising'] ?? 0;
    final falling = data['falling'] ?? 0;
    final unchanged = data['unchanged'] ?? 0;
    final total = rising + falling + unchanged;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              'Rising',
              rising.toString(),
              Colors.green,
              total > 0 ? (rising / total * 100).toStringAsFixed(1) + '%' : '0%',
            ),
            _buildStatItem(
              'Falling',
              falling.toString(),
              Colors.red,
              total > 0 ? (falling / total * 100).toStringAsFixed(1) + '%' : '0%',
            ),
            _buildStatItem(
              'Unchanged',
              unchanged.toString(),
              Colors.grey,
              total > 0 ? (unchanged / total * 100).toStringAsFixed(1) + '%' : '0%',
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (total > 0)
          LinearProgressIndicator(
            value: total > 0 ? rising / total : 0,
            backgroundColor: Colors.red.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            minHeight: 8,
          ),
      ],
    );
  }

  Widget _buildPlaceholderOverview() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('Rising', '--', Colors.green, '--%'),
            _buildStatItem('Falling', '--', Colors.red, '--%'),
            _buildStatItem('Unchanged', '--', Colors.grey, '--%'),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, Color color, String percentage) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 10,
            color: color,
          ),
        ),
      ],
    );
  }
}
