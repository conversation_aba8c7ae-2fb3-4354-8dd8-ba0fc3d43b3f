import 'package:freezed_annotation/freezed_annotation.dart';

part 'stock_info.freezed.dart';
part 'stock_info.g.dart';

@freezed
class StockInfo with _$StockInfo {
  const factory StockInfo({
    @Json<PERSON>ey(name: 'symbol') String? symbol,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'name') String? name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'price') double? price,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'change') double? change,
    @<PERSON>son<PERSON>ey(name: 'changePercent') double? changePercent,
    @<PERSON>son<PERSON><PERSON>(name: 'volume') int? volume,
    @<PERSON>son<PERSON><PERSON>(name: 'marketCap') double? marketCap,
    @Json<PERSON>ey(name: 'high') double? high,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'low') double? low,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'open') double? open,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'close') double? close,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') String? updateTime,
  }) = _StockInfo;

  factory StockInfo.fromJson(Map<String, dynamic> json) =>
      _$StockInfoFromJson(json);
}
