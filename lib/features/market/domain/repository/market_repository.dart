import '../../../../core/models/response_result.dart';
import '../models/stock_info.dart';

abstract class MarketRepository {
  /// Get stock list with pagination
  Future<ResponseResult<List<StockInfo>>> getStockList({
    int? pageNum,
    int? pageSize,
    String? order,
    int? sortType,
  });
  
  /// Search stocks by keyword
  Future<ResponseResult<List<StockInfo>>> searchStocks({
    required String keyword,
    int? pageNum,
    int? pageSize,
  });
  
  /// Get stock information by symbol
  Future<ResponseResult<StockInfo>> getStockInfo(String instrument);
  
  /// Get stock K-line data
  Future<ResponseResult<Map<String, dynamic>>> getStockKline({
    required String symbol,
    required String period,
  });
  
  /// Get market depth data
  Future<ResponseResult<Map<String, dynamic>>> getMarketDepth({
    required String instrument,
    int? depth,
  });
  
  /// Get timeline data
  Future<ResponseResult<Map<String, dynamic>>> getTimeline({
    required String instrument,
    required String period,
  });
  
  /// Get gain distribution
  Future<ResponseResult<Map<String, dynamic>>> getGainDistribution();
  
  /// Get market plate data
  Future<ResponseResult<Map<String, dynamic>>> getMarketPlate({
    String? field,
    String? marketType,
    String? order,
    int? page,
    int? pageSize,
    String? plate,
    String? securityType,
  });
}
