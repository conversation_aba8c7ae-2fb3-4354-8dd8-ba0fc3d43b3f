import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/market_repository.dart';
import '../models/stock_info.dart';

@Injectable(as: MarketRepository)
class MarketService implements MarketRepository {
  final NetworkProvider _networkProvider;

  MarketService(this._networkProvider);

  @override
  Future<ResponseResult<List<StockInfo>>> getStockList({
    int? pageNum,
    int? pageSize,
    String? order,
    int? sortType,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.getStockList,
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (order != null) 'order': order,
          if (sortType != null) 'sortType': sortType,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> stocksData = responseData['data']['records'] ?? [];
          final stocks = stocksData.map((json) => StockInfo.fromJson(json)).toList();
          return ResponseResult.success(stocks);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get stock list');
        }
      } else {
        return ResponseResult.error('Failed to get stock list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<StockInfo>>> searchStocks({
    required String keyword,
    int? pageNum,
    int? pageSize,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.getSearch,
        queryParameters: {
          'keyword': keyword,
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> stocksData = responseData['data'] ?? [];
          final stocks = stocksData.map((json) => StockInfo.fromJson(json)).toList();
          return ResponseResult.success(stocks);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to search stocks');
        }
      } else {
        return ResponseResult.error('Failed to search stocks');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<StockInfo>> getStockInfo(String instrument) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.getStockList,
        queryParameters: {'instrument': instrument},
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final stockInfo = StockInfo.fromJson(responseData['data']);
          return ResponseResult.success(stockInfo);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get stock info');
        }
      } else {
        return ResponseResult.error('Failed to get stock info');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getStockKline({
    required String symbol,
    required String period,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        '/market/getStockKline', // Direct path since this endpoint doesn't exist in current ApiEndpoints
        queryParameters: {
          'symbol': symbol,
          'period': period,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(responseData['data']);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get K-line data');
        }
      } else {
        return ResponseResult.error('Failed to get K-line data');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getMarketDepth({
    required String instrument,
    int? depth,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.depthQuote,
        queryParameters: {
          'instrument': instrument,
          if (depth != null) 'depth': depth,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(responseData['data']);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get market depth');
        }
      } else {
        return ResponseResult.error('Failed to get market depth');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getTimeline({
    required String instrument,
    required String period,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.timeLineMini,
        queryParameters: {
          'instrument': instrument,
          'period': period,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(responseData['data']);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get timeline data');
        }
      } else {
        return ResponseResult.error('Failed to get timeline data');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getGainDistribution() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.getGainDistribution,
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(responseData['data']);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get gain distribution');
        }
      } else {
        return ResponseResult.error('Failed to get gain distribution');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getMarketPlate({
    String? field,
    String? marketType,
    String? order,
    int? page,
    int? pageSize,
    String? plate,
    String? securityType,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        '/market/getMarketPlate', // Direct path since this endpoint doesn't exist in current ApiEndpoints
        queryParameters: {
          if (field != null) 'field': field,
          if (marketType != null) 'marketType': marketType,
          if (order != null) 'order': order,
          if (page != null) 'page': page,
          if (pageSize != null) 'pageSize': pageSize,
          if (plate != null) 'plate': plate,
          if (securityType != null) 'securityType': securityType,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(responseData['data']);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get market plate data');
        }
      } else {
        return ResponseResult.error('Failed to get market plate data');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['msg'] ?? 'Server error';
        return 'Error $statusCode: $message';
      case DioExceptionType.cancel:
        return 'Request cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      default:
        return 'Network error: ${e.message}';
    }
  }
}
