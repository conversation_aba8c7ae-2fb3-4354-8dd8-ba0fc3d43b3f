import 'package:freezed_annotation/freezed_annotation.dart';

part 'expend_address.freezed.dart';
part 'expend_address.g.dart';

@freezed
class ExpendAddress with _$ExpendAddress {
  const factory ExpendAddress({
    @Json<PERSON>ey(name: 'id') int? id,
    @J<PERSON><PERSON><PERSON>(name: 'userId') int? userId,
    @J<PERSON><PERSON><PERSON>(name: 'type') String? type,
    @Json<PERSON>ey(name: 'address') String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'label') String? label,
    @JsonKey(name: 'network') String? network,
    @<PERSON>son<PERSON>ey(name: 'currency') String? currency,
    @<PERSON>son<PERSON>ey(name: 'isDefault') @Default(false) bool isDefault,
    @J<PERSON><PERSON><PERSON>(name: 'status') int? status,
    @Json<PERSON>ey(name: 'createTime') String? createTime,
    @Json<PERSON>ey(name: 'updateTime') String? updateTime,
  }) = _ExpendAddress;

  factory ExpendAddress.from<PERSON>son(Map<String, dynamic> json) =>
      _$ExpendAddressFromJson(json);
}

@freezed
class ExpendAddressRequest with _$ExpendAddressRequest {
  const factory ExpendAddressRequest({
    @JsonKey(name: 'type') required String type,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'label') required String label,
    @JsonKey(name: 'network') required String network,
    @JsonKey(name: 'currency') required String currency,
    @JsonKey(name: 'isDefault') @Default(false) bool isDefault,
  }) = _ExpendAddressRequest;

  factory ExpendAddressRequest.fromJson(Map<String, dynamic> json) =>
      _$ExpendAddressRequestFromJson(json);
}

@freezed
class EmailCaptchaRequest with _$EmailCaptchaRequest {
  const factory EmailCaptchaRequest({
    @JsonKey(name: 'email') required String email,
    @JsonKey(name: 'type') required String type,
  }) = _EmailCaptchaRequest;

  factory EmailCaptchaRequest.fromJson(Map<String, dynamic> json) =>
      _$EmailCaptchaRequestFromJson(json);
}

@freezed
class EmailCaptchaVerifyRequest with _$EmailCaptchaVerifyRequest {
  const factory EmailCaptchaVerifyRequest({
    @JsonKey(name: 'email') required String email,
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'type') required String type,
  }) = _EmailCaptchaVerifyRequest;

  factory EmailCaptchaVerifyRequest.fromJson(Map<String, dynamic> json) =>
      _$EmailCaptchaVerifyRequestFromJson(json);
}

@freezed
class PasswordChangeRequest with _$PasswordChangeRequest {
  const factory PasswordChangeRequest({
    @JsonKey(name: 'oldPassword') required String oldPassword,
    @JsonKey(name: 'newPassword') required String newPassword,
    @JsonKey(name: 'confirmPassword') required String confirmPassword,
    @JsonKey(name: 'randomKey') required String randomKey,
  }) = _PasswordChangeRequest;

  factory PasswordChangeRequest.fromJson(Map<String, dynamic> json) =>
      _$PasswordChangeRequestFromJson(json);
}
