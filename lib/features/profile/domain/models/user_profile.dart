import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email') String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'username') String? username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'nickname') String? nickname,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar') String? avatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone') String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'realName') String? realName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'idCard') String? idCard,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'birthday') String? birthday,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'gender') int? gender,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'country') String? country,
    @<PERSON><PERSON><PERSON>ey(name: 'region') String? region,
    @<PERSON>son<PERSON>ey(name: 'address') String? address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'invitationCode') String? invitationCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'inviterCode') String? inviterCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'status') int? status,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') String? createTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'lastLoginTime') String? lastLoginTime,
    @JsonKey(name: 'isEmailVerified') @Default(false) bool isEmailVerified,
    @JsonKey(name: 'isPhoneVerified') @Default(false) bool isPhoneVerified,
    @JsonKey(name: 'isGoogleAuthEnabled') @Default(false) bool isGoogleAuthEnabled,
    @JsonKey(name: 'isIdentityVerified') @Default(false) bool isIdentityVerified,
    @JsonKey(name: 'isPayPasswordSet') @Default(false) bool isPayPasswordSet,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}
