import 'package:freezed_annotation/freezed_annotation.dart';

part 'google_token_info.freezed.dart';
part 'google_token_info.g.dart';

@freezed
class GoogleTokenInfo with _$GoogleTokenInfo {
  const factory GoogleTokenInfo({
    @Json<PERSON>ey(name: 'qrCode') String? qrCode,
    @J<PERSON><PERSON><PERSON>(name: 'secretKey') String? secretKey,
    @Json<PERSON>ey(name: 'backupCodes') List<String>? backupCodes,
    @Json<PERSON><PERSON>(name: 'isEnabled') @Default(false) bool isEnabled,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'setupTime') String? setupTime,
  }) = _GoogleTokenInfo;

  factory GoogleTokenInfo.fromJson(Map<String, dynamic> json) =>
      _$GoogleTokenInfoFromJson(json);
}

@freezed
class GoogleTokenResetRequest with _$GoogleTokenResetRequest {
  const factory GoogleTokenResetRequest({
    @J<PERSON><PERSON><PERSON>(name: 'randomKey') required String randomKey,
  }) = _GoogleTokenResetRequest;

  factory GoogleTokenResetRequest.fromJson(Map<String, dynamic> json) =>
      _$GoogleTokenResetRequestFromJson(json);
}

@freezed
class GoogleTokenVerifyRequest with _$GoogleTokenVerifyRequest {
  const factory GoogleTokenVerifyRequest({
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'randomKey') required String randomKey,
  }) = _GoogleTokenVerifyRequest;

  factory GoogleTokenVerifyRequest.fromJson(Map<String, dynamic> json) =>
      _$GoogleTokenVerifyRequestFromJson(json);
}
