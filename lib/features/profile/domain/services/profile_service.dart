import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/profile_repository.dart';
import '../models/user_profile.dart';
import '../models/expend_address.dart';
import '../models/google_token_info.dart';

@Injectable(as: ProfileRepository)
class ProfileService implements ProfileRepository {
  final NetworkProvider _networkProvider;

  ProfileService(this._networkProvider);

  @override
  Future<ResponseResult<UserProfile>> getProfileInfo() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileInfo,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: UserProfile.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get profile info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> sendEmailCaptcha(EmailCaptchaRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileEmailCaptchaSend,
        data: {
          'email': request.email,
          'type': request.type,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to send email captcha');
        }
      } else {
        return ResponseResult.error('Failed to send email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> checkEmailCaptcha(EmailCaptchaVerifyRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileEmailCaptchaCheck,
        data: {
          'email': request.email,
          'code': request.code,
          'type': request.type,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to verify email captcha');
        }
      } else {
        return ResponseResult.error('Failed to verify email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<List<ExpendAddress>>> getExpendAddresses() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileExpendAddress,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> addressList = response.data['data'] ?? [];
          final addresses = addressList
              .map((json) => ExpendAddress.fromJson(json))
              .toList();
          return ResponseResult(data: addresses);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get expend addresses');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> saveExpendAddress(ExpendAddressRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileExpendAddressSave,
        data: {
          'type': request.type,
          'address': request.address,
          'label': request.label,
          'network': request.network,
          'currency': request.currency,
          'isDefault': request.isDefault,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to save expend address');
        }
      } else {
        return ResponseResult.error('Failed to save expend address');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changeLoginPassword(PasswordChangeRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileLoginPasswordChange,
        data: {
          'oldPassword': request.oldPassword,
          'newPassword': request.newPassword,
          'confirmPassword': request.confirmPassword,
          'randomKey': request.randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to change login password');
        }
      } else {
        return ResponseResult.error('Failed to change login password');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changePayPassword(PasswordChangeRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profilePayPasswordChange,
        data: {
          'oldPassword': request.oldPassword,
          'newPassword': request.newPassword,
          'confirmPassword': request.confirmPassword,
          'randomKey': request.randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to change pay password');
        }
      } else {
        return ResponseResult.error('Failed to change pay password');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<GoogleTokenInfo>> resetGoogleToken(GoogleTokenResetRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileResetGoogleToken,
        data: {
          'randomKey': request.randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(
            GoogleTokenInfo.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to reset Google token');
        }
      } else {
        return ResponseResult.error('Failed to reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> checkResetGoogleToken(GoogleTokenVerifyRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileResetGoogleTokenCheck,
        data: {
          'code': request.code,
          'randomKey': request.randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to check reset Google token');
        }
      } else {
        return ResponseResult.error('Failed to check reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }
}
