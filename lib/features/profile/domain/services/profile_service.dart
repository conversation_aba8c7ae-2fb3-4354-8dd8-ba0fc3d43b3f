import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/profile_repository.dart';
import '../models/user_profile.dart';

@Injectable(as: ProfileRepository)
class ProfileService implements ProfileRepository {
  final NetworkProvider _networkProvider;

  ProfileService(this._networkProvider);

  @override
  Future<ResponseResult<UserProfile>> getProfileInfo() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileInfo,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: UserProfile.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get profile info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<String>> sendEmailCaptcha() async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileEmailCaptchaSend,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: response.data['data']['randomKey'] ?? '',
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to send email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<String>> checkEmailCaptcha({
    required String captcha,
    required String randomKey,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileEmailCaptchaCheck,
        data: {
          'captcha': captcha,
          'randomKey': randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: response.data['data']['randomKey'] ?? '',
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<List<ExpendAddress>>> getExpendAddresses() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileExpendAddress,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> addressList = response.data['data'] ?? [];
          final addresses = addressList
              .map((json) => ExpendAddress.fromJson(json))
              .toList();
          return ResponseResult(data: addresses);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get expend addresses');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> saveExpendAddress(ExpendAddressRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileExpendAddressSave,
        data: request.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to save expend address');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changeLoginPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileLoginPasswordChange,
        data: {
          'oldPassword': oldPassword,
          'newPassword': newPassword,
          'confirmPassword': confirmPassword,
          'randomKey': randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to change login password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changePayPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profilePayPasswordChange,
        data: {
          'oldPassword': oldPassword,
          'newPassword': newPassword,
          'confirmPassword': confirmPassword,
          'randomKey': randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to change pay password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<GoogleTokenInfo>> resetGoogleToken({
    required String randomKey,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileResetGoogleToken,
        data: {
          'randomKey': randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: GoogleTokenInfo.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> checkResetGoogleToken({
    required String code,
    required String randomKey,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.profileResetGoogleTokenCheck,
        data: {
          'code': code,
          'randomKey': randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }
}
