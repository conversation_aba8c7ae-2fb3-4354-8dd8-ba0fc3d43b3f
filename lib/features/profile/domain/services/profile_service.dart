import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/profile_repository.dart';
import '../models/user_profile.dart';
import '../models/expend_address.dart';
import '../models/google_token_info.dart';

@Injectable(as: ProfileRepository)
class ProfileService implements ProfileRepository {
  final NetworkProvider _networkProvider;

  ProfileService(this._networkProvider);

  @override
  Future<ResponseResult<UserProfile>> getProfileInfo() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileInfo,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: UserProfile.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get profile info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> sendEmailCaptcha(EmailCaptchaRequest request) async {
    try {
      // According to API docs, /profile/email_captcha/send is a GET request with no parameters
      final Response response = await _networkProvider.get(
        ApiEndpoints.emailCaptchaProfile,
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to send email captcha');
        }
      } else {
        return ResponseResult.error('Failed to send email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> checkEmailCaptcha(EmailCaptchaVerifyRequest request) async {
    try {
      // According to API docs, UserEmailCaptchaCheckReqVo requires 'code' and 'nonce' fields
      final Response response = await _networkProvider.post(
        ApiEndpoints.emailCodeCheck,
        data: {
          'code': request.code,
          'nonce': request.email, // Using email as nonce for now, should be the random key from send captcha
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to verify email captcha');
        }
      } else {
        return ResponseResult.error('Failed to verify email captcha');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<List<ExpendAddress>>> getExpendAddresses() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileExpendAddress,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> addressList = response.data['data'] ?? [];
          final addresses = addressList
              .map((json) => ExpendAddress.fromJson(json))
              .toList();
          return ResponseResult(data: addresses);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get expend addresses');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> saveExpendAddress(ExpendAddressRequest request) async {
    try {
      // According to API docs, 用户提现地址修改模型 requires address, googleCode, name, type
      final Response response = await _networkProvider.post(
        ApiEndpoints.expendAddress,
        data: {
          'address': request.address,
          'name': request.label, // API uses 'name' instead of 'label'
          'type': request.type, // ETHER or TRON
          'googleCode': '', // Required field, but can be empty if not using 2FA
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to save expend address');
        }
      } else {
        return ResponseResult.error('Failed to save expend address');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changeLoginPassword(PasswordChangeRequest request) async {
    try {
      // According to API docs, ChangePasswordReqVo requires code, confirmPassword, nonce, password
      final Response response = await _networkProvider.post(
        ApiEndpoints.changeLoginPassword,
        data: {
          'password': request.newPassword, // API uses 'password' for new password
          'confirmPassword': request.confirmPassword,
          'nonce': request.randomKey, // API uses 'nonce' instead of 'randomKey'
          'code': '', // Google verification code, can be empty if not using 2FA
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to change login password');
        }
      } else {
        return ResponseResult.error('Failed to change login password');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> changePayPassword(PasswordChangeRequest request) async {
    try {
      // According to API docs, ChangePayPasswordReqVo requires code, confirmPayPassword, nonce, payPassword
      final Response response = await _networkProvider.post(
        ApiEndpoints.changeWalletPassword,
        data: {
          'payPassword': request.newPassword, // API uses 'payPassword' for new password
          'confirmPayPassword': request.confirmPassword, // API uses 'confirmPayPassword'
          'nonce': request.randomKey, // API uses 'nonce' instead of 'randomKey'
          'code': '', // Google verification code, can be empty if not using 2FA
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to change pay password');
        }
      } else {
        return ResponseResult.error('Failed to change pay password');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<GoogleTokenInfo>> resetGoogleToken(GoogleTokenResetRequest request) async {
    try {
      // According to API docs, RestGoogleKeyReqVo requires emailCaptcha and nonce
      final Response response = await _networkProvider.post(
        ApiEndpoints.googleAuthReset,
        data: {
          'emailCaptcha': '', // Email captcha code
          'nonce': request.randomKey, // API uses 'nonce' instead of 'randomKey'
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          // Map the response to GoogleTokenInfo
          final data = response.data['data'];
          return ResponseResult.success(
            GoogleTokenInfo(
              qrCode: data['qrCode'],
              secretKey: data['secret'],
            ),
          );
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to reset Google token');
        }
      } else {
        return ResponseResult.error('Failed to reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> checkResetGoogleToken(GoogleTokenVerifyRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.googleAuthCheckReset,
        data: {
          'code': request.code,
          'randomKey': request.randomKey,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to check reset Google token');
        }
      } else {
        return ResponseResult.error('Failed to check reset Google token');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }
}
