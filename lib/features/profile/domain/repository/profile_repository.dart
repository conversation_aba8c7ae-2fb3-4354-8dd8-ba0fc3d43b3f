import '../../../../core/models/response_result.dart';
import '../models/user_profile.dart';
import '../models/expend_address.dart';
import '../models/google_token_info.dart';

abstract class ProfileRepository {
  /// Get user profile information
  Future<ResponseResult<UserProfile>> getProfileInfo();

  /// Send email captcha for profile operations
  Future<ResponseResult<bool>> sendEmailCaptcha(EmailCaptchaRequest request);

  /// Check email captcha for profile operations
  Future<ResponseResult<bool>> checkEmailCaptcha(EmailCaptchaVerifyRequest request);

  /// Get user withdrawal addresses
  Future<ResponseResult<List<ExpendAddress>>> getExpendAddresses();

  /// Save new withdrawal address
  Future<ResponseResult<bool>> saveExpendAddress(ExpendAddressRequest request);

  /// Change login password
  Future<ResponseResult<bool>> changeLoginPassword(PasswordChangeRequest request);

  /// Change payment password
  Future<ResponseResult<bool>> changePayPassword(PasswordChangeRequest request);

  /// Reset Google authenticator token
  Future<ResponseResult<GoogleTokenInfo>> resetGoogleToken(GoogleTokenResetRequest request);

  /// Check reset Google token
  Future<ResponseResult<bool>> checkResetGoogleToken(GoogleTokenVerifyRequest request);
}


