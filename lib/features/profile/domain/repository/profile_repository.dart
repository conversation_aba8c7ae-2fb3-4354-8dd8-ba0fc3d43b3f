import '../../../../core/models/response_result.dart';
import '../models/user_profile.dart';

abstract class ProfileRepository {
  /// Get user profile information
  Future<ResponseResult<UserProfile>> getProfileInfo();
  
  /// Send email captcha for profile operations
  Future<ResponseResult<String>> sendEmailCaptcha();
  
  /// Check email captcha for profile operations
  Future<ResponseResult<String>> checkEmailCaptcha({
    required String captcha,
    required String randomKey,
  });
  
  /// Get user withdrawal addresses
  Future<ResponseResult<List<ExpendAddress>>> getExpendAddresses();
  
  /// Save new withdrawal address
  Future<ResponseResult<bool>> saveExpendAddress(ExpendAddressRequest request);
  
  /// Change login password
  Future<ResponseResult<bool>> changeLoginPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  });
  
  /// Change payment password
  Future<ResponseResult<bool>> changePayPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  });
  
  /// Reset Google authenticator token
  Future<ResponseResult<GoogleTokenInfo>> resetGoogleToken({
    required String randomKey,
  });
  
  /// Check reset Google token
  Future<ResponseResult<bool>> checkResetGoogleToken({
    required String code,
    required String randomKey,
  });
}

class ExpendAddress {
  final int? id;
  final String? address;
  final String? label;
  final String? network;
  final String? createTime;
  final String? updateTime;

  ExpendAddress({
    this.id,
    this.address,
    this.label,
    this.network,
    this.createTime,
    this.updateTime,
  });

  factory ExpendAddress.fromJson(Map<String, dynamic> json) {
    return ExpendAddress(
      id: json['id'],
      address: json['address'],
      label: json['label'],
      network: json['network'],
      createTime: json['createTime'],
      updateTime: json['updateTime'],
    );
  }
}

class ExpendAddressRequest {
  final String address;
  final String label;
  final String network;
  final String payPassword;

  ExpendAddressRequest({
    required this.address,
    required this.label,
    required this.network,
    required this.payPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'label': label,
      'network': network,
      'payPassword': payPassword,
    };
  }
}

class GoogleTokenInfo {
  final String? secret;
  final String? qrCode;
  final String? randomKey;

  GoogleTokenInfo({
    this.secret,
    this.qrCode,
    this.randomKey,
  });

  factory GoogleTokenInfo.fromJson(Map<String, dynamic> json) {
    return GoogleTokenInfo(
      secret: json['secret'],
      qrCode: json['qrCode'],
      randomKey: json['randomKey'],
    );
  }
}
