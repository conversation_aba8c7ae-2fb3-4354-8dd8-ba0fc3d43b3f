import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/profile_repository.dart';
import '../domain/models/user_profile.dart';
import '../domain/models/expend_address.dart';
import '../domain/models/google_token_info.dart';
import 'profile_state.dart';

@injectable
class ProfileCubit extends Cubit<ProfileState> {
  final ProfileRepository _profileRepository;

  ProfileCubit(this._profileRepository) : super(const ProfileState());

  Future<void> loadProfile() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _profileRepository.getProfileInfo();

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoading: false,
          profile: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading profile: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load profile: $e',
      ));
    }
  }

  Future<void> sendEmailCaptcha(String email, String type) async {
    emit(state.copyWith(isEmailCaptchaLoading: true, emailCaptchaError: null));

    try {
      final request = EmailCaptchaRequest(email: email, type: type);
      final result = await _profileRepository.sendEmailCaptcha(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isEmailCaptchaLoading: false,
          isEmailCaptchaSent: true,
          emailCaptchaError: null,
        ));
      } else {
        emit(state.copyWith(
          isEmailCaptchaLoading: false,
          emailCaptchaError: result.error,
        ));
      }
    } catch (e) {
      log('Error sending email captcha: $e');
      emit(state.copyWith(
        isEmailCaptchaLoading: false,
        emailCaptchaError: 'Failed to send email captcha: $e',
      ));
    }
  }

  Future<bool> checkEmailCaptcha(String email, String code, String type) async {
    emit(state.copyWith(isEmailCaptchaLoading: true, emailCaptchaError: null));

    try {
      final request = EmailCaptchaVerifyRequest(email: email, code: code, type: type);
      final result = await _profileRepository.checkEmailCaptcha(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isEmailCaptchaLoading: false,
          isEmailCaptchaVerified: true,
          emailCaptchaError: null,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isEmailCaptchaLoading: false,
          emailCaptchaError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error checking email captcha: $e');
      emit(state.copyWith(
        isEmailCaptchaLoading: false,
        emailCaptchaError: 'Failed to verify email captcha: $e',
      ));
      return false;
    }
  }

  Future<void> loadExpendAddresses() async {
    emit(state.copyWith(isAddressLoading: true, addressError: null));

    try {
      final result = await _profileRepository.getExpendAddresses();

      if (result.isSuccess) {
        emit(state.copyWith(
          isAddressLoading: false,
          expendAddresses: result.data ?? [],
          addressError: null,
        ));
      } else {
        emit(state.copyWith(
          isAddressLoading: false,
          addressError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading expend addresses: $e');
      emit(state.copyWith(
        isAddressLoading: false,
        addressError: 'Failed to load addresses: $e',
      ));
    }
  }

  Future<bool> saveExpendAddress({
    required String type,
    required String address,
    required String label,
    required String network,
    required String currency,
    bool isDefault = false,
  }) async {
    emit(state.copyWith(isAddressLoading: true, addressError: null));

    try {
      final request = ExpendAddressRequest(
        type: type,
        address: address,
        label: label,
        network: network,
        currency: currency,
        isDefault: isDefault,
      );

      final result = await _profileRepository.saveExpendAddress(request);

      if (result.isSuccess) {
        emit(state.copyWith(isAddressLoading: false, addressError: null));
        // Reload addresses after saving
        await loadExpendAddresses();
        return true;
      } else {
        emit(state.copyWith(
          isAddressLoading: false,
          addressError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error saving expend address: $e');
      emit(state.copyWith(
        isAddressLoading: false,
        addressError: 'Failed to save address: $e',
      ));
      return false;
    }
  }

  Future<bool> changeLoginPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  }) async {
    if (!state.isEmailCaptchaVerified) {
      emit(state.copyWith(passwordError: 'Email verification required'));
      return false;
    }

    emit(state.copyWith(isPasswordChanging: true, passwordError: null));

    try {
      final request = PasswordChangeRequest(
        oldPassword: oldPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
        randomKey: randomKey,
      );

      final result = await _profileRepository.changeLoginPassword(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isPasswordChanging: false,
          passwordError: null,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isPasswordChanging: false,
          passwordError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error changing login password: $e');
      emit(state.copyWith(
        isPasswordChanging: false,
        passwordError: 'Failed to change login password: $e',
      ));
      return false;
    }
  }

  Future<bool> changePayPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
    required String randomKey,
  }) async {
    if (!state.isEmailCaptchaVerified) {
      emit(state.copyWith(passwordError: 'Email verification required'));
      return false;
    }

    emit(state.copyWith(isPasswordChanging: true, passwordError: null));

    try {
      final request = PasswordChangeRequest(
        oldPassword: oldPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
        randomKey: randomKey,
      );

      final result = await _profileRepository.changePayPassword(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isPasswordChanging: false,
          passwordError: null,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isPasswordChanging: false,
          passwordError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error changing pay password: $e');
      emit(state.copyWith(
        isPasswordChanging: false,
        passwordError: 'Failed to change pay password: $e',
      ));
      return false;
    }
  }

  Future<GoogleTokenInfo?> resetGoogleToken(String randomKey) async {
    emit(state.copyWith(isGoogleTokenLoading: true, googleTokenError: null));

    try {
      final request = GoogleTokenResetRequest(randomKey: randomKey);
      final result = await _profileRepository.resetGoogleToken(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isGoogleTokenLoading: false,
          googleTokenInfo: result.data,
          googleTokenError: null,
        ));
        return result.data;
      } else {
        emit(state.copyWith(
          isGoogleTokenLoading: false,
          googleTokenError: result.error,
        ));
        return null;
      }
    } catch (e) {
      log('Error resetting Google token: $e');
      emit(state.copyWith(
        isGoogleTokenLoading: false,
        googleTokenError: 'Failed to reset Google token: $e',
      ));
      return null;
    }
  }

  Future<bool> checkResetGoogleToken(String code, String randomKey) async {
    emit(state.copyWith(isGoogleTokenLoading: true, googleTokenError: null));

    try {
      final request = GoogleTokenVerifyRequest(code: code, randomKey: randomKey);
      final result = await _profileRepository.checkResetGoogleToken(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isGoogleTokenLoading: false,
          googleTokenError: null,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isGoogleTokenLoading: false,
          googleTokenError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error checking Google token reset: $e');
      emit(state.copyWith(
        isGoogleTokenLoading: false,
        googleTokenError: 'Failed to verify Google token reset: $e',
      ));
      return false;
    }
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      emailCaptchaError: null,
      addressError: null,
      passwordError: null,
      googleTokenError: null,
    ));
  }

  /// Reset email captcha state
  void resetEmailCaptcha() {
    emit(state.copyWith(
      isEmailCaptchaSent: false,
      isEmailCaptchaVerified: false,
      emailCaptchaError: null,
    ));
  }
}
