import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/profile_repository.dart';
import '../domain/models/user_profile.dart';
import 'profile_state.dart';

@injectable
class ProfileCubit extends Cubit<ProfileState> {
  final ProfileRepository _profileRepository;

  ProfileCubit(this._profileRepository) : super(const ProfileState());

  Future<void> loadProfile() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.getProfileInfo();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        profile: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<void> sendEmailCaptcha() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.sendEmailCaptcha();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        emailCaptchaKey: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> checkEmailCaptcha(String captcha) async {
    if (state.emailCaptchaKey == null) {
      emit(state.copyWith(error: 'No captcha key available'));
      return false;
    }

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.checkEmailCaptcha(
      captcha: captcha,
      randomKey: state.emailCaptchaKey!,
    );

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        verifiedCaptchaKey: result.data,
        error: null,
      ));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> loadExpendAddresses() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.getExpendAddresses();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        expendAddresses: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<bool> saveExpendAddress(ExpendAddressRequest request) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.saveExpendAddress(request);

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      // Reload addresses after saving
      await loadExpendAddresses();
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<bool> changeLoginPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    if (state.verifiedCaptchaKey == null) {
      emit(state.copyWith(error: 'Email verification required'));
      return false;
    }

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.changeLoginPassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      randomKey: state.verifiedCaptchaKey!,
    );

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<bool> changePayPassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    if (state.verifiedCaptchaKey == null) {
      emit(state.copyWith(error: 'Email verification required'));
      return false;
    }

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.changePayPassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      randomKey: state.verifiedCaptchaKey!,
    );

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<GoogleTokenInfo?> resetGoogleToken() async {
    if (state.verifiedCaptchaKey == null) {
      emit(state.copyWith(error: 'Email verification required'));
      return null;
    }

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.resetGoogleToken(
      randomKey: state.verifiedCaptchaKey!,
    );

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        googleTokenInfo: result.data,
        error: null,
      ));
      return result.data;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return null;
    }
  }

  Future<bool> checkResetGoogleToken(String code) async {
    if (state.googleTokenInfo?.randomKey == null) {
      emit(state.copyWith(error: 'No Google token reset in progress'));
      return false;
    }

    emit(state.copyWith(isLoading: true, error: null));

    final result = await _profileRepository.checkResetGoogleToken(
      code: code,
      randomKey: state.googleTokenInfo!.randomKey!,
    );

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }

  void clearCaptchaKeys() {
    emit(state.copyWith(
      emailCaptchaKey: null,
      verifiedCaptchaKey: null,
    ));
  }
}
