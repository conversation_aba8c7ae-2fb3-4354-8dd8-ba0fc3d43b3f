import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/user_profile.dart';
import '../domain/models/expend_address.dart';
import '../domain/models/google_token_info.dart';

part 'profile_state.freezed.dart';

@freezed
class ProfileState with _$ProfileState {
  const factory ProfileState({
    @Default(false) bool isLoading,
    @Default(false) bool isEmailCaptchaLoading,
    @Default(false) bool isAddressLoading,
    @Default(false) bool isPasswordChanging,
    @Default(false) bool isGoogleTokenLoading,
    @Default(null) UserProfile? profile,
    @Default(null) String? error,
    @Default(null) String? emailCaptchaError,
    @Default(null) String? addressError,
    @Default(null) String? passwordError,
    @Default(null) String? googleTokenError,
    @Default([]) List<ExpendAddress> expendAddresses,
    @Default(null) GoogleTokenInfo? googleTokenInfo,
    @Default(false) bool isEmailCaptchaSent,
    @Default(false) bool isEmailCaptchaVerified,
  }) = _ProfileState;
}
