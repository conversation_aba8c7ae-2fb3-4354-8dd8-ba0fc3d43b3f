import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/user_profile.dart';
import '../domain/repository/profile_repository.dart';

part 'profile_state.freezed.dart';

@freezed
class ProfileState with _$ProfileState {
  const factory ProfileState({
    @Default(false) bool isLoading,
    @Default(null) UserProfile? profile,
    @Default(null) String? error,
    @Default(null) String? emailCaptchaKey,
    @Default(null) String? verifiedCaptchaKey,
    @Default(null) List<ExpendAddress>? expendAddresses,
    @Default(null) GoogleTokenInfo? googleTokenInfo,
  }) = _ProfileState;
}
