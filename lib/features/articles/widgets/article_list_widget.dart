import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/article_cubit.dart';
import '../logic/article_state.dart';
import '../domain/models/article.dart';
import 'article_item_widget.dart';
import 'article_detail_screen.dart';

class ArticleListWidget extends StatefulWidget {
  const ArticleListWidget({super.key});

  @override
  State<ArticleListWidget> createState() => _ArticleListWidgetState();
}

class _ArticleListWidgetState extends State<ArticleListWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<ArticleCubit>().state;
      if (!state.isLoading && state.hasMoreData) {
        context.read<ArticleCubit>().loadMoreArticles();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocBuilder<ArticleCubit, ArticleState>(
      builder: (context, state) {
        if (state.isLoading && state.articles.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state.error != null && state.articles.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[400],
                ),
                const SizedBox(height: 16),
                Text(
                  state.error!,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.read<ArticleCubit>().refreshArticles(),
                  child: Text(l10n.common_retry),
                ),
              ],
            ),
          );
        }

        if (state.articles.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.article_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No articles found',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Try adjusting your filters or search terms',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => context.read<ArticleCubit>().refreshArticles(),
          child: ListView.builder(
            controller: _scrollController,
            itemCount: state.articles.length + (state.hasMoreData ? 1 : 0),
            itemBuilder: (context, index) {
              if (index >= state.articles.length) {
                // Loading indicator for pagination
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final article = state.articles[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: ArticleItemWidget(
                  article: article,
                  onTap: () => _onArticleTap(context, article),
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _onArticleTap(BuildContext context, Article article) {
    if (article.id != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => BlocProvider.value(
            value: context.read<ArticleCubit>(),
            child: ArticleDetailScreen(articleId: article.id!),
          ),
        ),
      );
    }
  }
}
