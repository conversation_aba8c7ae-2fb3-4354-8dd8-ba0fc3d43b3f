import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/article_cubit.dart';

class ArticleSearchWidget extends StatefulWidget {
  const ArticleSearchWidget({super.key});

  @override
  State<ArticleSearchWidget> createState() => _ArticleSearchWidgetState();
}

class _ArticleSearchWidgetState extends State<ArticleSearchWidget> {
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final state = context.read<ArticleCubit>().state;
    _searchController.text = state.filterKeyword ?? '';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return AlertDialog(
      title: const Text('Search Articles'),
      content: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          labelText: 'Enter keywords',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.search),
        ),
        autofocus: true,
        onSubmitted: (_) => _performSearch(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.common_cancel),
        ),
        TextButton(
          onPressed: _clearSearch,
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: _performSearch,
          child: const Text('Search'),
        ),
      ],
    );
  }

  void _performSearch() {
    final keyword = _searchController.text.trim();
    if (keyword.isNotEmpty) {
      context.read<ArticleCubit>().searchArticles(keyword);
    } else {
      context.read<ArticleCubit>().clearFilters();
    }
    Navigator.of(context).pop();
  }

  void _clearSearch() {
    _searchController.clear();
    context.read<ArticleCubit>().clearFilters();
    Navigator.of(context).pop();
  }
}
