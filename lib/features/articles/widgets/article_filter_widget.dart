import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/article_cubit.dart';

class ArticleFilterWidget extends StatefulWidget {
  const ArticleFilterWidget({super.key});

  @override
  State<ArticleFilterWidget> createState() => _ArticleFilterWidgetState();
}

class _ArticleFilterWidgetState extends State<ArticleFilterWidget> {
  String? _selectedCategory;
  String? _selectedTag;
  bool? _selectedIsFeatured;
  String? _selectedLanguage;

  final List<String> _languageOptions = [
    'en',
    'zh',
    'es',
    'fr',
    'de',
    'ja',
    'ko',
  ];

  @override
  void initState() {
    super.initState();
    final state = context.read<ArticleCubit>().state;
    _selectedCategory = state.filterCategory;
    _selectedTag = state.filterTag;
    _selectedIsFeatured = state.filterIsFeatured;
    _selectedLanguage = state.filterLanguage;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final state = context.read<ArticleCubit>().state;
    
    return AlertDialog(
      title: const Text('Filter Articles'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Category filter
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All Categories'),
                ),
                ...state.availableCategories.map((category) => DropdownMenuItem<String>(
                  value: category,
                  child: Text(category),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Tag filter
            DropdownButtonFormField<String>(
              value: _selectedTag,
              decoration: const InputDecoration(
                labelText: 'Tag',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All Tags'),
                ),
                ...state.availableTags.map((tag) => DropdownMenuItem<String>(
                  value: tag,
                  child: Text(tag),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedTag = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Featured filter
            DropdownButtonFormField<bool>(
              value: _selectedIsFeatured,
              decoration: const InputDecoration(
                labelText: 'Featured',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem<bool>(
                  value: null,
                  child: Text('All Articles'),
                ),
                DropdownMenuItem<bool>(
                  value: true,
                  child: Text('Featured Only'),
                ),
                DropdownMenuItem<bool>(
                  value: false,
                  child: Text('Non-Featured Only'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsFeatured = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Language filter
            DropdownButtonFormField<String>(
              value: _selectedLanguage,
              decoration: const InputDecoration(
                labelText: 'Language',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All Languages'),
                ),
                ..._languageOptions.map((language) => DropdownMenuItem<String>(
                  value: language,
                  child: Text(_getLanguageName(language)),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.common_cancel),
        ),
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('Apply'),
        ),
      ],
    );
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'zh':
        return 'Chinese';
      case 'es':
        return 'Spanish';
      case 'fr':
        return 'French';
      case 'de':
        return 'German';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      default:
        return code.toUpperCase();
    }
  }

  void _applyFilters() {
    context.read<ArticleCubit>().applyFilters(
      category: _selectedCategory,
      tag: _selectedTag,
      isFeatured: _selectedIsFeatured,
      language: _selectedLanguage,
    );

    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedTag = null;
      _selectedIsFeatured = null;
      _selectedLanguage = null;
    });

    context.read<ArticleCubit>().clearFilters();
    Navigator.of(context).pop();
  }
}
