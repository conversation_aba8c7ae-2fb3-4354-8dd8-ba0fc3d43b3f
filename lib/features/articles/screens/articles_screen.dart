import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/article_cubit.dart';
import '../logic/article_state.dart';
import '../widgets/article_list_widget.dart';
import '../widgets/article_filter_widget.dart';
import '../widgets/article_search_widget.dart';

class ArticlesScreen extends StatelessWidget {
  const ArticlesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocProvider(
      create: (context) => getIt<ArticleCubit>()..loadArticles(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Articles'),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showSearchDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(context),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(context, value),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'featured',
                  child: Row(
                    children: [
                      Icon(Icons.star),
                      SizedBox(width: 8),
                      Text('Featured Articles'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'refresh',
                  child: Row(
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: 8),
                      Text('Refresh'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'clear_filters',
                  child: Row(
                    children: [
                      Icon(Icons.clear),
                      SizedBox(width: 8),
                      Text('Clear Filters'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: Column(
          children: [
            // Filter chips
            BlocBuilder<ArticleCubit, ArticleState>(
              builder: (context, state) {
                final hasFilters = state.filterCategory != null ||
                    state.filterKeyword != null ||
                    state.filterTag != null ||
                    state.filterIsFeatured == true ||
                    state.filterLanguage != null;

                if (!hasFilters) return const SizedBox.shrink();

                return Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      if (state.filterCategory != null)
                        _buildFilterChip(
                          context,
                          'Category: ${state.filterCategory}',
                          () => context.read<ArticleCubit>().filterByCategory(''),
                        ),
                      if (state.filterKeyword != null)
                        _buildFilterChip(
                          context,
                          'Search: ${state.filterKeyword}',
                          () => context.read<ArticleCubit>().searchArticles(''),
                        ),
                      if (state.filterTag != null)
                        _buildFilterChip(
                          context,
                          'Tag: ${state.filterTag}',
                          () => context.read<ArticleCubit>().filterByTag(''),
                        ),
                      if (state.filterIsFeatured == true)
                        _buildFilterChip(
                          context,
                          'Featured',
                          () => context.read<ArticleCubit>().clearFilters(),
                        ),
                      if (state.filterLanguage != null)
                        _buildFilterChip(
                          context,
                          'Language: ${state.filterLanguage}',
                          () => context.read<ArticleCubit>().clearFilters(),
                        ),
                    ],
                  ),
                );
              },
            ),
            
            // Article list
            const Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0),
                child: ArticleListWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context, String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onRemove,
      backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ArticleSearchWidget(),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ArticleFilterWidget(),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    final cubit = context.read<ArticleCubit>();
    
    switch (action) {
      case 'featured':
        cubit.loadFeaturedArticles();
        break;
      case 'refresh':
        cubit.refreshArticles();
        break;
      case 'clear_filters':
        cubit.clearFilters();
        break;
    }
  }
}
