import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/article_repository.dart';
import '../domain/models/article.dart';
import 'article_state.dart';

@injectable
class ArticleCubit extends Cubit<ArticleState> {
  final ArticleRepository _articleRepository;

  ArticleCubit(this._articleRepository) : super(const ArticleState());

  /// Load articles with pagination and filters
  Future<void> loadArticles({
    int? pageNum,
    int? pageSize,
    String? category,
    String? keyword,
    String? tag,
    bool? isFeatured,
    String? language,
    bool isRefresh = false,
  }) async {
    if (isRefresh) {
      emit(state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 1,
        articles: [],
      ));
    } else {
      emit(state.copyWith(isLoading: true, error: null));
    }

    try {
      final result = await _articleRepository.getArticleList(
        pageNum: pageNum ?? (isRefresh ? 1 : state.currentPage),
        pageSize: pageSize ?? state.pageSize,
        category: category ?? state.filterCategory,
        keyword: keyword ?? state.filterKeyword,
        tag: tag ?? state.filterTag,
        isFeatured: isFeatured ?? state.filterIsFeatured,
        language: language ?? state.filterLanguage,
      );

      if (result.isSuccess) {
        final newArticles = result.data ?? [];
        final updatedArticles = isRefresh || state.currentPage == 1
            ? newArticles
            : [...state.articles, ...newArticles];

        // Extract categories and tags for filtering
        final categories = <String>{};
        final tags = <String>{};
        
        for (final article in updatedArticles) {
          if (article.category != null) {
            categories.add(article.category!);
          }
          if (article.tags != null) {
            tags.addAll(article.tags!);
          }
        }

        emit(state.copyWith(
          isLoading: false,
          articles: updatedArticles,
          error: null,
          currentPage: (pageNum ?? state.currentPage) + 1,
          hasMoreData: newArticles.length >= (pageSize ?? state.pageSize),
          filterCategory: category ?? state.filterCategory,
          filterKeyword: keyword ?? state.filterKeyword,
          filterTag: tag ?? state.filterTag,
          filterIsFeatured: isFeatured ?? state.filterIsFeatured,
          filterLanguage: language ?? state.filterLanguage,
          availableCategories: categories.toList()..sort(),
          availableTags: tags.toList()..sort(),
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading articles: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load articles: $e',
      ));
    }
  }

  /// Load article details
  Future<void> loadArticleDetails(int articleId) async {
    emit(state.copyWith(isLoadingDetails: true, detailsError: null));

    try {
      final result = await _articleRepository.getArticleDetails(articleId);

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoadingDetails: false,
          selectedArticle: result.data,
          detailsError: null,
        ));
      } else {
        emit(state.copyWith(
          isLoadingDetails: false,
          detailsError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading article details: $e');
      emit(state.copyWith(
        isLoadingDetails: false,
        detailsError: 'Failed to load article details: $e',
      ));
    }
  }

  /// Apply filters and reload articles
  Future<void> applyFilters({
    String? category,
    String? keyword,
    String? tag,
    bool? isFeatured,
    String? language,
  }) async {
    await loadArticles(
      category: category,
      keyword: keyword,
      tag: tag,
      isFeatured: isFeatured,
      language: language,
      isRefresh: true,
    );
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await loadArticles(
      category: null,
      keyword: null,
      tag: null,
      isFeatured: null,
      language: null,
      isRefresh: true,
    );
  }

  /// Load more articles (pagination)
  Future<void> loadMoreArticles() async {
    if (!state.isLoading && state.hasMoreData) {
      await loadArticles();
    }
  }

  /// Refresh articles
  Future<void> refreshArticles() async {
    await loadArticles(isRefresh: true);
  }

  /// Load featured articles only
  Future<void> loadFeaturedArticles() async {
    await loadArticles(isFeatured: true, isRefresh: true);
  }

  /// Search articles by keyword
  Future<void> searchArticles(String keyword) async {
    await loadArticles(keyword: keyword, isRefresh: true);
  }

  /// Filter by category
  Future<void> filterByCategory(String category) async {
    await loadArticles(category: category, isRefresh: true);
  }

  /// Filter by tag
  Future<void> filterByTag(String tag) async {
    await loadArticles(tag: tag, isRefresh: true);
  }

  /// Clear selected article
  void clearSelectedArticle() {
    emit(state.copyWith(
      selectedArticle: null,
      detailsError: null,
    ));
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      detailsError: null,
    ));
  }
}
