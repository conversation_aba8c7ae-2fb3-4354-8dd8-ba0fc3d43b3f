import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/article.dart';

part 'article_state.freezed.dart';

@freezed
class ArticleState with _$ArticleState {
  const factory ArticleState({
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingDetails,
    @Default([]) List<Article> articles,
    @Default(null) Article? selectedArticle,
    @Default(null) String? error,
    @Default(null) String? detailsError,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(false) bool hasMoreData,
    @Default(null) String? filterCategory,
    @Default(null) String? filterKeyword,
    @Default(null) String? filterTag,
    @Default(null) bool? filterIsFeatured,
    @Default(null) String? filterLanguage,
    @Default([]) List<String> availableCategories,
    @Default([]) List<String> availableTags,
  }) = _ArticleState;
}
