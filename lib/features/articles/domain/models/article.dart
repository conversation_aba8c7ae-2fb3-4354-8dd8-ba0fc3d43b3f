import 'package:freezed_annotation/freezed_annotation.dart';

part 'article.freezed.dart';
part 'article.g.dart';

@freezed
class Article with _$Article {
  const factory Article({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') String? title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'content') String? content,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'summary') String? summary,
    @<PERSON><PERSON><PERSON>ey(name: 'category') String? category,
    @<PERSON>son<PERSON>ey(name: 'author') String? author,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'authorAvatar') String? authorAvatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'coverImage') String? coverImage,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'tags') List<String>? tags,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isPublished') @Default(false) bool isPublished,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isFeatured') @Default(false) bool isFeatured,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'viewCount') int? viewCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'likeCount') int? likeCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'shareCount') int? shareCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'publishTime') String? publishTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') String? createTime,
    @Json<PERSON>ey(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'readingTime') int? readingTime, // in minutes
    @JsonKey(name: 'language') String? language,
    @JsonKey(name: 'seoTitle') String? seoTitle,
    @JsonKey(name: 'seoDescription') String? seoDescription,
  }) = _Article;

  factory Article.fromJson(Map<String, dynamic> json) =>
      _$ArticleFromJson(json);
}

@freezed
class ArticleQuery with _$ArticleQuery {
  const factory ArticleQuery({
    @JsonKey(name: 'pageNum') int? pageNum,
    @JsonKey(name: 'pageSize') int? pageSize,
    @JsonKey(name: 'category') String? category,
    @JsonKey(name: 'keyword') String? keyword,
    @JsonKey(name: 'tag') String? tag,
    @JsonKey(name: 'isFeatured') bool? isFeatured,
    @JsonKey(name: 'language') String? language,
  }) = _ArticleQuery;

  factory ArticleQuery.fromJson(Map<String, dynamic> json) =>
      _$ArticleQueryFromJson(json);
}
