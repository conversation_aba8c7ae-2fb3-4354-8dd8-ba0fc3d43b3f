import '../../../../core/models/response_result.dart';
import '../models/article.dart';

abstract class ArticleRepository {
  /// Get article list with pagination and filters
  Future<ResponseResult<List<Article>>> getArticleList({
    int? pageNum,
    int? pageSize,
    String? category,
    String? keyword,
    String? tag,
    bool? isFeatured,
    String? language,
  });

  /// Get article details by ID
  Future<ResponseResult<Article>> getArticleDetails(int articleId);
}
