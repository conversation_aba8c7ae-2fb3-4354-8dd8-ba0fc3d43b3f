import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/article_repository.dart';
import '../models/article.dart';

@Injectable(as: ArticleRepository)
class ArticleService implements ArticleRepository {
  final NetworkProvider _networkProvider;

  ArticleService(this._networkProvider);

  @override
  Future<ResponseResult<List<Article>>> getArticleList({
    int? pageNum,
    int? pageSize,
    String? category,
    String? keyword,
    String? tag,
    bool? isFeatured,
    String? language,
  }) async {
    try {
      // According to API docs, /article/page supports pageNum, pageSize, status, title, type
      final Response response = await _networkProvider.get(
        ApiEndpoints.articlePage,
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (keyword != null) 'title': keyword, // API uses 'title' for search
          if (category != null) 'type': int.tryParse(category) ?? 1, // API expects int type
          'status': 1, // Only get published articles
        },
        isAuthRequired: false, // Articles might be public
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['list'] ?? [];
          final articles = data.map((json) => _mapArticleVoToArticle(json)).toList();
          return ResponseResult.success(articles);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get article list');
        }
      } else {
        return ResponseResult.error('Failed to get article list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Article>> getArticleDetails(int articleId) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.articleDetails,
        queryParameters: {
          'id': articleId,
        },
        isAuthRequired: false, // Article details might be public
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final article = _mapArticleDetailBOToArticle(responseData['data']);
          return ResponseResult.success(article);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get article details');
        }
      } else {
        return ResponseResult.error('Failed to get article details');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  /// Map ArticleVo from API to Article model
  Article _mapArticleVoToArticle(Map<String, dynamic> json) {
    return Article(
      id: json['id'],
      title: json['title'],
      summary: json['remark'], // API uses 'remark' for summary
      coverImage: json['coverUrl'], // API uses 'coverUrl'
      isPublished: json['status'] == 1, // status 1 means published
      publishTime: json['createTime'],
      createTime: json['createTime'],
      updateTime: json['modifiedTime'],
      category: json['type']?.toString(), // Convert int type to string
    );
  }

  /// Map ArticleDetailBO from API to Article model
  Article _mapArticleDetailBOToArticle(Map<String, dynamic> json) {
    return Article(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      summary: json['remark'], // API uses 'remark' for summary
      coverImage: json['coverUrl'], // API uses 'coverUrl'
      isPublished: json['status'] == 1, // status 1 means published
      publishTime: json['createTime'],
      createTime: json['createTime'],
      updateTime: json['modifiedTime'],
      category: json['type']?.toString(), // Convert int type to string
    );
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        return 'Server error: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      default:
        return e.message ?? 'Network error occurred';
    }
  }
}
