import 'package:freezed_annotation/freezed_annotation.dart';

part 'announcement.freezed.dart';
part 'announcement.g.dart';

@freezed
class Announcement with _$Announcement {
  const factory Announcement({
    @Json<PERSON>ey(name: 'id') int? id,
    @Json<PERSON>ey(name: 'title') String? title,
    @Json<PERSON>ey(name: 'content') String? content,
    @Json<PERSON>ey(name: 'type') int? type, // API returns int64, not string
    @Json<PERSON>ey(name: 'haveRead') @Default(false) bool haveRead, // API uses 'haveRead'
    @<PERSON><PERSON><PERSON><PERSON>(name: 'startTime') String? startTime, // API uses 'startTime'
    @<PERSON><PERSON><PERSON><PERSON>(name: 'endTime') String? endTime, // API uses 'endTime'
  }) = _Announcement;

  factory Announcement.fromJson(Map<String, dynamic> json) =>
      _$AnnouncementFromJson(json);
}

@freezed
class AnnouncementQuery with _$AnnouncementQuery {
  const factory AnnouncementQuery({
    @J<PERSON><PERSON><PERSON>(name: 'pageNum') int? pageNum,
    @Json<PERSON>ey(name: 'pageSize') int? pageSize,
    @Json<PERSON>ey(name: 'type') String? type,
    @J<PERSON><PERSON><PERSON>(name: 'isRead') bool? isRead,
    @JsonKey(name: 'keyword') String? keyword,
  }) = _AnnouncementQuery;

  factory AnnouncementQuery.fromJson(Map<String, dynamic> json) =>
      _$AnnouncementQueryFromJson(json);
}
