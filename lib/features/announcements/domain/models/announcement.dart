import 'package:freezed_annotation/freezed_annotation.dart';

part 'announcement.freezed.dart';
part 'announcement.g.dart';

@freezed
class Announcement with _$Announcement {
  const factory Announcement({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') String? title,
    @Json<PERSON><PERSON>(name: 'content') String? content,
    @Json<PERSON><PERSON>(name: 'type') String? type,
    @Json<PERSON><PERSON>(name: 'priority') int? priority,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isRead') @Default(false) bool isRead,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isPublished') @Default(false) bool isPublished,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'publishTime') String? publishTime,
    @J<PERSON><PERSON><PERSON>(name: 'createTime') String? createTime,
    @J<PERSON><PERSON><PERSON>(name: 'updateTime') String? updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'author') String? author,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'tags') List<String>? tags,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'imageUrl') String? imageUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'readCount') int? readCount,
  }) = _Announcement;

  factory Announcement.from<PERSON>son(Map<String, dynamic> json) =>
      _$AnnouncementFromJson(json);
}

@freezed
class AnnouncementQuery with _$AnnouncementQuery {
  const factory AnnouncementQuery({
    @JsonKey(name: 'pageNum') int? pageNum,
    @JsonKey(name: 'pageSize') int? pageSize,
    @JsonKey(name: 'type') String? type,
    @JsonKey(name: 'isRead') bool? isRead,
    @JsonKey(name: 'keyword') String? keyword,
  }) = _AnnouncementQuery;

  factory AnnouncementQuery.fromJson(Map<String, dynamic> json) =>
      _$AnnouncementQueryFromJson(json);
}
