import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/announcement_repository.dart';
import '../models/announcement.dart';

@Injectable(as: AnnouncementRepository)
class AnnouncementService implements AnnouncementRepository {
  final NetworkProvider _networkProvider;

  AnnouncementService(this._networkProvider);

  @override
  Future<ResponseResult<List<Announcement>>> getAnnouncementList({
    int? pageNum,
    int? pageSize,
    String? type,
    bool? isRead,
    String? keyword,
  }) async {
    try {
      // According to API docs, /announcement/info is a POST request with no body
      final Response response = await _networkProvider.post(
        ApiEndpoints.announcementInfo,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data'] ?? [];
          final announcements = data.map((json) => _mapAnnouncementVoToAnnouncement(json)).toList();
          return ResponseResult.success(announcements);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get announcement list');
        }
      } else {
        return ResponseResult.error('Failed to get announcement list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> markAnnouncementAsRead(int announcementId) async {
    try {
      // According to API docs, /announcement/have_read requires AnnouncementHaveReadVo with id field
      final Response response = await _networkProvider.post(
        ApiEndpoints.announcementHaveRead,
        data: {
          'id': announcementId,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to mark announcement as read');
        }
      } else {
        return ResponseResult.error('Failed to mark announcement as read');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  /// Map AnnouncementVo from API to Announcement model
  Announcement _mapAnnouncementVoToAnnouncement(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      type: json['type']?.toString(), // API returns int64, convert to string
      isRead: json['haveRead'] ?? false, // API uses 'haveRead' field
      publishTime: json['startTime'], // API uses 'startTime' for publish time
      createTime: json['startTime'],
      updateTime: json['endTime'], // API uses 'endTime' for update time
    );
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        return 'Server error: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      default:
        return e.message ?? 'Network error occurred';
    }
  }
}
