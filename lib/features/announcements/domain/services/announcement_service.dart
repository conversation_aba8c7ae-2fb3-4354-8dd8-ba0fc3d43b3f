import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/announcement_repository.dart';
import '../models/announcement.dart';

@Injectable(as: AnnouncementRepository)
class AnnouncementService implements AnnouncementRepository {
  final NetworkProvider _networkProvider;

  AnnouncementService(this._networkProvider);

  @override
  Future<ResponseResult<List<Announcement>>> getAnnouncementList({
    int? pageNum,
    int? pageSize,
    String? type,
    bool? isRead,
    String? keyword,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.announcementList,
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (type != null) 'type': type,
          if (isRead != null) 'isRead': isRead,
          if (keyword != null) 'keyword': keyword,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final announcements = data.map((json) => Announcement.fromJson(json)).toList();
          return ResponseResult.success(announcements);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get announcement list');
        }
      } else {
        return ResponseResult.error('Failed to get announcement list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> markAnnouncementAsRead(int announcementId) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.announcementRead,
        data: {
          'announcementId': announcementId,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to mark announcement as read');
        }
      } else {
        return ResponseResult.error('Failed to mark announcement as read');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        return 'Server error: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      default:
        return e.message ?? 'Network error occurred';
    }
  }
}
