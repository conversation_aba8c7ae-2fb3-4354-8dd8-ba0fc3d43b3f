import '../../../../core/models/response_result.dart';
import '../models/announcement.dart';

abstract class AnnouncementRepository {
  /// Get announcement list with pagination and filters
  Future<ResponseResult<List<Announcement>>> getAnnouncementList({
    int? pageNum,
    int? pageSize,
    String? type,
    bool? isRead,
    String? keyword,
  });

  /// Mark announcement as read
  Future<ResponseResult<bool>> markAnnouncementAsRead(int announcementId);
}
