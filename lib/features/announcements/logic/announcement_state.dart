import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/announcement.dart';

part 'announcement_state.freezed.dart';

@freezed
class AnnouncementState with _$AnnouncementState {
  const factory AnnouncementState({
    @Default(false) bool isLoading,
    @Default(false) bool isMarkingRead,
    @Default([]) List<Announcement> announcements,
    @Default(null) String? error,
    @Default(null) String? markReadError,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(false) bool hasMoreData,
    @Default(null) String? filterType,
    @Default(null) bool? filterIsRead,
    @Default(null) String? filterKeyword,
    @Default(0) int unreadCount,
  }) = _AnnouncementState;
}
