import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/announcement_repository.dart';
import '../domain/models/announcement.dart';
import 'announcement_state.dart';

@injectable
class AnnouncementCubit extends Cubit<AnnouncementState> {
  final AnnouncementRepository _announcementRepository;

  AnnouncementCubit(this._announcementRepository) : super(const AnnouncementState());

  /// Load announcements with pagination and filters
  Future<void> loadAnnouncements({
    int? pageNum,
    int? pageSize,
    String? type,
    bool? isRead,
    String? keyword,
    bool isRefresh = false,
  }) async {
    if (isRefresh) {
      emit(state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 1,
        announcements: [],
      ));
    } else {
      emit(state.copyWith(isLoading: true, error: null));
    }

    try {
      final result = await _announcementRepository.getAnnouncementList(
        pageNum: pageNum ?? (isRefresh ? 1 : state.currentPage),
        pageSize: pageSize ?? state.pageSize,
        type: type ?? state.filterType,
        isRead: isRead ?? state.filterIsRead,
        keyword: keyword ?? state.filterKeyword,
      );

      if (result.isSuccess) {
        final newAnnouncements = result.data ?? [];
        final updatedAnnouncements = isRefresh || state.currentPage == 1
            ? newAnnouncements
            : [...state.announcements, ...newAnnouncements];

        // Calculate unread count
        final unreadCount = updatedAnnouncements.where((a) => !a.isRead).length;

        emit(state.copyWith(
          isLoading: false,
          announcements: updatedAnnouncements,
          error: null,
          currentPage: (pageNum ?? state.currentPage) + 1,
          hasMoreData: newAnnouncements.length >= (pageSize ?? state.pageSize),
          filterType: type ?? state.filterType,
          filterIsRead: isRead ?? state.filterIsRead,
          filterKeyword: keyword ?? state.filterKeyword,
          unreadCount: unreadCount,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading announcements: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load announcements: $e',
      ));
    }
  }

  /// Mark announcement as read
  Future<bool> markAnnouncementAsRead(int announcementId) async {
    emit(state.copyWith(isMarkingRead: true, markReadError: null));

    try {
      final result = await _announcementRepository.markAnnouncementAsRead(announcementId);

      if (result.isSuccess) {
        // Update the announcement in the list
        final updatedAnnouncements = state.announcements.map((announcement) {
          if (announcement.id == announcementId) {
            return announcement.copyWith(isRead: true);
          }
          return announcement;
        }).toList();

        // Recalculate unread count
        final unreadCount = updatedAnnouncements.where((a) => !a.isRead).length;

        emit(state.copyWith(
          isMarkingRead: false,
          announcements: updatedAnnouncements,
          markReadError: null,
          unreadCount: unreadCount,
        ));
        return true;
      } else {
        emit(state.copyWith(
          isMarkingRead: false,
          markReadError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error marking announcement as read: $e');
      emit(state.copyWith(
        isMarkingRead: false,
        markReadError: 'Failed to mark announcement as read: $e',
      ));
      return false;
    }
  }

  /// Apply filters and reload announcements
  Future<void> applyFilters({
    String? type,
    bool? isRead,
    String? keyword,
  }) async {
    await loadAnnouncements(
      type: type,
      isRead: isRead,
      keyword: keyword,
      isRefresh: true,
    );
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await loadAnnouncements(
      type: null,
      isRead: null,
      keyword: null,
      isRefresh: true,
    );
  }

  /// Load more announcements (pagination)
  Future<void> loadMoreAnnouncements() async {
    if (!state.isLoading && state.hasMoreData) {
      await loadAnnouncements();
    }
  }

  /// Refresh announcements
  Future<void> refreshAnnouncements() async {
    await loadAnnouncements(isRefresh: true);
  }

  /// Mark all announcements as read
  Future<void> markAllAsRead() async {
    final unreadAnnouncements = state.announcements.where((a) => !a.isRead).toList();
    
    for (final announcement in unreadAnnouncements) {
      if (announcement.id != null) {
        await markAnnouncementAsRead(announcement.id!);
      }
    }
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      markReadError: null,
    ));
  }
}
