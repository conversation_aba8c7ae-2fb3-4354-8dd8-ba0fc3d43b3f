import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/announcement_cubit.dart';
import '../logic/announcement_state.dart';
import '../widgets/announcement_list_widget.dart';
import '../widgets/announcement_filter_widget.dart';

class AnnouncementsScreen extends StatelessWidget {
  const AnnouncementsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocProvider(
      create: (context) => getIt<AnnouncementCubit>()..loadAnnouncements(),
      child: Scaffold(
        appBar: AppBar(
          title: BlocBuilder<AnnouncementCubit, AnnouncementState>(
            builder: (context, state) {
              return Row(
                children: [
                  const Text('Announcements'),
                  if (state.unreadCount > 0) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${state.unreadCount}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(context),
            ),
            BlocBuilder<AnnouncementCubit, AnnouncementState>(
              builder: (context, state) {
                if (state.unreadCount > 0) {
                  return IconButton(
                    icon: const Icon(Icons.mark_email_read),
                    onPressed: () => context.read<AnnouncementCubit>().markAllAsRead(),
                    tooltip: 'Mark all as read',
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => context.read<AnnouncementCubit>().refreshAnnouncements(),
            ),
          ],
        ),
        body: const Padding(
          padding: EdgeInsets.all(16.0),
          child: AnnouncementListWidget(),
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AnnouncementFilterWidget(),
    );
  }
}
