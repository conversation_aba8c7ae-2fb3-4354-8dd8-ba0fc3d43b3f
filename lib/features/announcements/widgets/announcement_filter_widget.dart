import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/announcement_cubit.dart';

class AnnouncementFilterWidget extends StatefulWidget {
  const AnnouncementFilterWidget({super.key});

  @override
  State<AnnouncementFilterWidget> createState() => _AnnouncementFilterWidgetState();
}

class _AnnouncementFilterWidgetState extends State<AnnouncementFilterWidget> {
  final _keywordController = TextEditingController();
  String? _selectedType;
  bool? _selectedIsRead;

  final List<String> _typeOptions = [
    'SYSTEM',
    'MAINTENANCE',
    'PROMOTION',
    'UPDATE',
    'SECURITY',
    'GENERAL',
  ];

  @override
  void initState() {
    super.initState();
    final state = context.read<AnnouncementCubit>().state;
    _keywordController.text = state.filterKeyword ?? '';
    _selectedType = state.filterType;
    _selectedIsRead = state.filterIsRead;
  }

  @override
  void dispose() {
    _keywordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return AlertDialog(
      title: const Text('Filter Announcements'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Keyword search
            TextField(
              controller: _keywordController,
              decoration: const InputDecoration(
                labelText: 'Search keyword',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Type filter
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Type',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All Types'),
                ),
                ..._typeOptions.map((type) => DropdownMenuItem<String>(
                  value: type,
                  child: Text(type),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Read status filter
            DropdownButtonFormField<bool>(
              value: _selectedIsRead,
              decoration: const InputDecoration(
                labelText: 'Read Status',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem<bool>(
                  value: null,
                  child: Text('All'),
                ),
                DropdownMenuItem<bool>(
                  value: false,
                  child: Text('Unread'),
                ),
                DropdownMenuItem<bool>(
                  value: true,
                  child: Text('Read'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedIsRead = value;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.common_cancel),
        ),
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('Apply'),
        ),
      ],
    );
  }

  void _applyFilters() {
    final keyword = _keywordController.text.trim().isEmpty 
        ? null 
        : _keywordController.text.trim();

    context.read<AnnouncementCubit>().applyFilters(
      type: _selectedType,
      isRead: _selectedIsRead,
      keyword: keyword,
    );

    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _keywordController.clear();
      _selectedType = null;
      _selectedIsRead = null;
    });

    context.read<AnnouncementCubit>().clearFilters();
    Navigator.of(context).pop();
  }
}
