import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_info.freezed.dart';
part 'user_info.g.dart';

@freezed
class UserInfo with _$UserInfo {
  const factory UserInfo({
    @J<PERSON><PERSON>ey(name: 'auth') @Default(false) bool auth, // Authentication status
    @<PERSON>sonKey(name: 'email') String? email,
    @<PERSON>sonKey(name: 'level') int? level, // User level
    @JsonKey(name: 'username') String? username,
  }) = _UserInfo;

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);
}
