import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_info.freezed.dart';
part 'user_info.g.dart';

@freezed
class UserInfo with _$UserInfo {
  const factory UserInfo({
    @J<PERSON><PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email') String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'username') String? username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'nickname') String? nickname,
    @<PERSON>son<PERSON><PERSON>(name: 'avatar') String? avatar,
    @<PERSON>son<PERSON><PERSON>(name: 'phone') String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'status') int? status,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') String? createTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') String? updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'lastLoginTime') String? lastLoginTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isEmailVerified') @Default(false) bool isEmailVerified,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isPhoneVerified') @Default(false) bool isPhoneVerified,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isGoogleAuthEnabled') @Default(false) bool isGoogleAuthEnabled,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isIdentityVerified') @Default(false) bool isIdentityVerified,
  }) = _UserInfo;

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);
}
