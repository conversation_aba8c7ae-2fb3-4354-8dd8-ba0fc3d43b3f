import 'package:freezed_annotation/freezed_annotation.dart';

part 'captcha.freezed.dart';
part 'captcha.g.dart';

@freezed
class Captcha with _$Captcha {
  const factory Captcha({
    @Json<PERSON>ey(name: 'captchaId') String? captchaId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'captchaImage') String? captchaImage,
    @Json<PERSON>ey(name: 'expireTime') int? expireTime,
  }) = _Captcha;

  factory Captcha.fromJson(Map<String, dynamic> json) =>
      _$CaptchaFromJson(json);
}
