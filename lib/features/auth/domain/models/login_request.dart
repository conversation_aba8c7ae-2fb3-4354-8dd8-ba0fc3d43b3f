import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_request.freezed.dart';
part 'login_request.g.dart';

@freezed
class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    @Json<PERSON>ey(name: 'username') required String username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'password') required String password,
    @J<PERSON><PERSON>ey(name: 'captcha') String? captcha,
    @J<PERSON><PERSON>ey(name: 'k') String? k,
  }) = _LoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}
