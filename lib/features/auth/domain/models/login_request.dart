import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_request.freezed.dart';
part 'login_request.g.dart';

@freezed
class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    @Json<PERSON>ey(name: 'email') required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'password') required String password,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'captcha') String? captcha,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'captchaId') String? captchaId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'rememberMe') @Default(false) bool rememberMe,
  }) = _LoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);
}
