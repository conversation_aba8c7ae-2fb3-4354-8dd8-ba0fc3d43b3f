import 'package:freezed_annotation/freezed_annotation.dart';

part 'register_request.freezed.dart';
part 'register_request.g.dart';

@freezed
class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    @Json<PERSON>ey(name: 'username') required String username, // 用户名
    @<PERSON><PERSON><PERSON><PERSON>(name: 'password') required String password, // 登录密码
    @JsonKey(name: 'otp') required String otp, // 邮箱验证码
    @JsonKey(name: 'nonce') required String nonce, // 邮箱验证码随机数
    @JsonKey(name: 'invitationCode') String? invitationCode, // 邀请码
  }) = _RegisterRequest;

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}
