import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/storage/secure_storage_helper.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/constants/local_storage_keys.dart';
import '../repository/auth_repository.dart';
import '../models/login_request.dart';
import '../models/user_info.dart';
import '../models/captcha.dart';

@Injectable(as: AuthRepository)
class AuthService implements AuthRepository {
  final NetworkProvider _networkProvider;
  final SecureStorageHelper _secureStorage;

  AuthService(this._networkProvider, this._secureStorage);

  @override
  Future<ResponseResult<UserInfo>> login(LoginRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.login,
        data: request.toJson(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final userInfo = UserInfo.fromJson(responseData['data']);
          
          // Save token from response headers
          final token = response.headers.value('Authorization') ?? 
                       response.headers.value('authorization');
          if (token != null) {
            await saveAuthToken(token);
          }
          
          return ResponseResult.success(userInfo, token: token);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Login failed');
        }
      } else {
        return ResponseResult.error('Login failed with status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> logout() async {
    try {
      final Response response = await _networkProvider.get(
        '/logout', // Direct path since logout endpoint doesn't exist in current ApiEndpoints
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        await clearAuthToken();
        return ResponseResult.success(true);
      } else {
        return ResponseResult.error('Logout failed');
      }
    } on DioException catch (e) {
      // Even if logout fails on server, clear local token
      await clearAuthToken();
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      await clearAuthToken();
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<Captcha>> getLoginCaptcha() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.loginCaptcha,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final captcha = Captcha.fromJson(responseData['data']);
          return ResponseResult.success(captcha);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get captcha');
        }
      } else {
        return ResponseResult.error('Failed to get captcha');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<bool> isSignedIn() async {
    final token = await getAuthToken();
    return token != null && token.isNotEmpty;
  }

  @override
  Future<ResponseResult<UserInfo>> getCurrentUser() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.profileInfo,
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final userInfo = UserInfo.fromJson(responseData['data']);
          return ResponseResult.success(userInfo);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get user info');
        }
      } else {
        return ResponseResult.error('Failed to get user info');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<void> saveAuthToken(String token) async {
    await _secureStorage.writeSecureData(LocalStorageKeys.token, token);
  }

  @override
  Future<void> clearAuthToken() async {
    await _secureStorage.deleteSecureData(LocalStorageKeys.token);
  }

  @override
  Future<String?> getAuthToken() async {
    return await _secureStorage.readSecureData(LocalStorageKeys.token);
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['msg'] ?? 'Server error';
        return 'Error $statusCode: $message';
      case DioExceptionType.cancel:
        return 'Request cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      default:
        return 'Network error: ${e.message}';
    }
  }
}
