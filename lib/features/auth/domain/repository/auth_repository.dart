import '../../../../core/models/response_result.dart';
import '../models/login_request.dart';
import '../models/user_info.dart';
import '../models/captcha.dart';

abstract class AuthRepository {
  /// User login
  Future<ResponseResult<UserInfo>> login(LoginRequest request);
  
  /// User logout
  Future<ResponseResult<bool>> logout();
  
  /// Get login captcha
  Future<ResponseResult<Captcha>> getLoginCaptcha();
  
  /// Check if user is signed in
  Future<bool> isSignedIn();
  
  /// Get current user info
  Future<ResponseResult<UserInfo>> getCurrentUser();
  
  /// Save auth token
  Future<void> saveAuthToken(String token);
  
  /// Clear auth token
  Future<void> clearAuthToken();
  
  /// Get stored auth token
  Future<String?> getAuthToken();
}
