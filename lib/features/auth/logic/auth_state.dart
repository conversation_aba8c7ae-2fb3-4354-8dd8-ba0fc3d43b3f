import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/user_info.dart';
import '../domain/models/captcha.dart';

part 'auth_state.freezed.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isLoading,
    @Default(false) bool isAuthenticated,
    UserInfo? user,
    Captcha? captcha,
    String? error,
    @Default(false) bool isLoginLoading,
    @Default(false) bool isLogoutLoading,
    @Default(false) bool isCaptchaLoading,
  }) = _AuthState;
}
