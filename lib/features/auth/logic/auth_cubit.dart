import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/auth_repository.dart';
import '../domain/models/login_request.dart';
import '../domain/models/user_info.dart';
import 'auth_state.dart';

@injectable
class AuthCubit extends Cubit<AuthState> {
  final AuthRepository _authRepository;

  AuthCubit(this._authRepository) : super(const AuthState());

  /// Initialize auth state by checking if user is already signed in
  Future<void> initialize() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final isSignedIn = await _authRepository.isSignedIn();

      if (isSignedIn) {
        final userResult = await _authRepository.getCurrentUser();

        if (userResult.isSuccess) {
          emit(state.copyWith(
            isLoading: false,
            isAuthenticated: true,
            user: userResult.data,
          ));
        } else {
          // Token exists but invalid, clear it
          await _authRepository.clearAuthToken();
          emit(state.copyWith(
            isLoading: false,
            isAuthenticated: false,
            error: userResult.error,
          ));
        }
      } else {
        emit(state.copyWith(
          isLoading: false,
          isAuthenticated: false,
        ));
      }
    } catch (e) {
      log('Error initializing auth: $e', name: 'AuthCubit');
      emit(state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        error: 'Failed to initialize authentication',
      ));
    }
  }

  /// Login user
  Future<void> login(LoginRequest request) async {
    emit(state.copyWith(isLoginLoading: true, error: null));

    try {
      final result = await _authRepository.login(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoginLoading: false,
          isAuthenticated: true,
          user: result.data,
        ));
        log('Login successful', name: 'AuthCubit');
      } else {
        emit(state.copyWith(
          isLoginLoading: false,
          error: result.error,
        ));
        log('Login failed: ${result.error}', name: 'AuthCubit');
      }
    } catch (e) {
      log('Login error: $e', name: 'AuthCubit');
      emit(state.copyWith(
        isLoginLoading: false,
        error: 'Login failed: $e',
      ));
    }
  }

  /// Logout user
  Future<void> logout() async {
    emit(state.copyWith(isLogoutLoading: true, error: null));

    try {
      final result = await _authRepository.logout();

      if (result.isSuccess) {
        emit(state.copyWith(
          isLogoutLoading: false,
          isAuthenticated: false,
          user: null,
        ));
        log('Logout successful', name: 'AuthCubit');
      } else {
        emit(state.copyWith(
          isLogoutLoading: false,
          error: result.error,
        ));
        log('Logout failed: ${result.error}', name: 'AuthCubit');
      }
    } catch (e) {
      log('Logout error: $e', name: 'AuthCubit');
      emit(state.copyWith(
        isLogoutLoading: false,
        error: 'Logout failed: $e',
      ));
    }
  }

  /// Get login captcha
  Future<void> getCaptcha() async {
    emit(state.copyWith(isCaptchaLoading: true, error: null));

    try {
      final result = await _authRepository.getLoginCaptcha();

      if (result.isSuccess) {
        emit(state.copyWith(
          isCaptchaLoading: false,
          captcha: result.data,
        ));
        log('Captcha loaded successfully', name: 'AuthCubit');
      } else {
        emit(state.copyWith(
          isCaptchaLoading: false,
          error: result.error,
        ));
        log('Failed to load captcha: ${result.error}', name: 'AuthCubit');
      }
    } catch (e) {
      log('Captcha error: $e', name: 'AuthCubit');
      emit(state.copyWith(
        isCaptchaLoading: false,
        error: 'Failed to load captcha: $e',
      ));
    }
  }

  /// Clear error state
  void clearError() {
    emit(state.copyWith(error: null));
  }

  /// Update user info
  void updateUser(UserInfo user) {
    emit(state.copyWith(user: user));
  }
}
