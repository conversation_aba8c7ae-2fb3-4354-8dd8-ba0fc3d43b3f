import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/stock_transaction_cubit.dart';

class StockTransactionFilterWidget extends StatefulWidget {
  const StockTransactionFilterWidget({super.key});

  @override
  State<StockTransactionFilterWidget> createState() => _StockTransactionFilterWidgetState();
}

class _StockTransactionFilterWidgetState extends State<StockTransactionFilterWidget> {
  final _orderNoController = TextEditingController();
  final _symbolController = TextEditingController();
  String? _selectedAction;
  DateTime? _startDate;
  DateTime? _endDate;

  final List<String> _actionOptions = ['BUY', 'SELL'];

  @override
  void initState() {
    super.initState();
    final state = context.read<StockTransactionCubit>().state;
    _orderNoController.text = state.filterOrderNo ?? '';
    _symbolController.text = state.filterSymbol ?? '';
    _selectedAction = state.filterAction;
    
    if (state.filterStartTime != null) {
      try {
        _startDate = DateTime.parse(state.filterStartTime!);
      } catch (e) {
        // Ignore parsing errors
      }
    }
    
    if (state.filterEndTime != null) {
      try {
        _endDate = DateTime.parse(state.filterEndTime!);
      } catch (e) {
        // Ignore parsing errors
      }
    }
  }

  @override
  void dispose() {
    _orderNoController.dispose();
    _symbolController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return AlertDialog(
      title: const Text('Filter Transactions'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Order Number
            TextField(
              controller: _orderNoController,
              decoration: const InputDecoration(
                labelText: 'Order Number',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Symbol
            TextField(
              controller: _symbolController,
              decoration: const InputDecoration(
                labelText: 'Symbol',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Action
            DropdownButtonFormField<String>(
              value: _selectedAction,
              decoration: const InputDecoration(
                labelText: 'Action',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('All'),
                ),
                ..._actionOptions.map((action) => DropdownMenuItem<String>(
                  value: action,
                  child: Text(action),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedAction = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Start Date
            InkWell(
              onTap: () => _selectDate(context, true),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Start Date',
                  border: OutlineInputBorder(),
                ),
                child: Text(
                  _startDate != null 
                      ? '${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}'
                      : 'Select start date',
                  style: TextStyle(
                    color: _startDate != null ? null : Colors.grey[600],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // End Date
            InkWell(
              onTap: () => _selectDate(context, false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'End Date',
                  border: OutlineInputBorder(),
                ),
                child: Text(
                  _endDate != null 
                      ? '${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}'
                      : 'Select end date',
                  style: TextStyle(
                    color: _endDate != null ? null : Colors.grey[600],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.common_cancel),
        ),
        TextButton(
          onPressed: _clearFilters,
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: _applyFilters,
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate 
          ? (_startDate ?? DateTime.now())
          : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  void _applyFilters() {
    final orderNo = _orderNoController.text.trim().isEmpty 
        ? null 
        : _orderNoController.text.trim();
    final symbol = _symbolController.text.trim().isEmpty 
        ? null 
        : _symbolController.text.trim();
    final startTime = _startDate?.toIso8601String();
    final endTime = _endDate?.toIso8601String();

    context.read<StockTransactionCubit>().applyFilters(
      orderNo: orderNo,
      symbol: symbol,
      action: _selectedAction,
      startTime: startTime,
      endTime: endTime,
    );

    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _orderNoController.clear();
      _symbolController.clear();
      _selectedAction = null;
      _startDate = null;
      _endDate = null;
    });

    context.read<StockTransactionCubit>().clearFilters();
    Navigator.of(context).pop();
  }
}
