import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/stock_transaction_cubit.dart';
import '../logic/stock_transaction_state.dart';
import '../domain/models/stock_transaction.dart';
import 'stock_transaction_item_widget.dart';

class StockTransactionListWidget extends StatefulWidget {
  const StockTransactionListWidget({super.key});

  @override
  State<StockTransactionListWidget> createState() => _StockTransactionListWidgetState();
}

class _StockTransactionListWidgetState extends State<StockTransactionListWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<StockTransactionCubit>().state;
      if (!state.isLoading && state.hasMoreData) {
        context.read<StockTransactionCubit>().loadMoreTransactions();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocBuilder<StockTransactionCubit, StockTransactionState>(
      builder: (context, state) {
        if (state.isLoading && state.transactions.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state.error != null && state.transactions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[400],
                ),
                const SizedBox(height: 16),
                Text(
                  state.error!,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.read<StockTransactionCubit>().refreshTransactions(),
                  child: Text(l10n.common_retry),
                ),
              ],
            ),
          );
        }

        if (state.transactions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No stock transactions found',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => context.read<StockTransactionCubit>().refreshTransactions(),
          child: ListView.builder(
            controller: _scrollController,
            itemCount: state.transactions.length + (state.hasMoreData ? 1 : 0),
            itemBuilder: (context, index) {
              if (index >= state.transactions.length) {
                // Loading indicator for pagination
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final transaction = state.transactions[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: StockTransactionItemWidget(
                  transaction: transaction,
                ),
              );
            },
          ),
        );
      },
    );
  }
}
