import 'package:flutter/material.dart';
import '../domain/models/stock_transaction.dart';

class StockTransactionItemWidget extends StatelessWidget {
  final StockTransaction transaction;

  const StockTransactionItemWidget({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context) {
    final isProfit = (transaction.profit ?? 0) >= 0;
    final profitColor = isProfit ? Colors.green : Colors.red;
    final isBuy = transaction.action?.toUpperCase() == 'BUY';
    final actionColor = isBuy ? Colors.blue : Colors.orange;
    
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with symbol and action
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.symbol ?? 'N/A',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (transaction.symbolName != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          transaction.symbolName!,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: actionColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    transaction.action ?? 'N/A',
                    style: TextStyle(
                      color: actionColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Transaction details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Quantity',
                    '${transaction.quantity?.toStringAsFixed(2) ?? '0'}',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Price',
                    '\$${transaction.price?.toStringAsFixed(2) ?? '0.00'}',
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Amount',
                    '\$${transaction.amount?.toStringAsFixed(2) ?? '0.00'}',
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Profit and fee
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Profit',
                    '${isProfit ? '+' : ''}\$${transaction.profit?.toStringAsFixed(2) ?? '0.00'}',
                    valueColor: profitColor,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Profit Rate',
                    '${isProfit ? '+' : ''}${((transaction.profitRate ?? 0) * 100).toStringAsFixed(2)}%',
                    valueColor: profitColor,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Fee',
                    '\$${transaction.fee?.toStringAsFixed(2) ?? '0.00'}',
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Order number and time
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order No.',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        transaction.orderNo ?? 'N/A',
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Execute Time',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      _formatDateTime(transaction.executeTime),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
            
            if (transaction.remark != null && transaction.remark!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Remark: ${transaction.remark}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, {Color? valueColor}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  String _formatDateTime(String? dateTime) {
    if (dateTime == null) return 'N/A';
    try {
      final dt = DateTime.parse(dateTime);
      return '${dt.month}/${dt.day} ${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTime;
    }
  }
}
