import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/stock_transaction_cubit.dart';
import '../logic/stock_transaction_state.dart';
import '../widgets/stock_transaction_list_widget.dart';
import '../widgets/stock_transaction_filter_widget.dart';

class StockTransactionsScreen extends StatelessWidget {
  const StockTransactionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocProvider(
      create: (context) => getIt<StockTransactionCubit>()..loadTransactions(),
      child: Scaffold(
        appBar: AppBar(
          title: Text('Stock Transactions'),
          actions: [
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: () => _showFilterDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => context.read<StockTransactionCubit>().refreshTransactions(),
            ),
          ],
        ),
        body: const Padding(
          padding: EdgeInsets.all(16.0),
          child: StockTransactionListWidget(),
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const StockTransactionFilterWidget(),
    );
  }
}
