import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../repository/stock_transaction_repository.dart';
import '../models/stock_transaction.dart';

@Injectable(as: StockTransactionRepository)
class StockTransactionService implements StockTransactionRepository {
  final NetworkProvider _networkProvider;

  StockTransactionService(this._networkProvider);

  @override
  Future<ResponseResult<List<StockTransaction>>> getStockTransactionRecords({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? symbol,
    String? action,
    String? startTime,
    String? endTime,
  }) async {
    try {
      // According to API docs, /stock/transaction/page supports pageNum, pageSize, orderNo
      final Response response = await _networkProvider.get(
        '/stock/transaction/page', // Direct path since current endpoints use function with parameters
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (orderNo != null) 'orderNo': orderNo,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['result']['list'] ?? [];
          final transactions = data.map((json) => _mapTransactionRecordToStockTransaction(json)).toList();
          return ResponseResult.success(transactions);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get stock transaction records');
        }
      } else {
        return ResponseResult.error('Failed to get stock transaction records');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  /// Map 交易记录表查询模型 from API to StockTransaction model
  StockTransaction _mapTransactionRecordToStockTransaction(Map<String, dynamic> json) {
    return StockTransaction(
      id: json['id'],
      orderNo: json['orderNo'],
      userId: json['userId'],
      symbol: json['productName'], // API uses 'productName' for symbol
      symbolName: json['productName'],
      action: json['status'] == 0 ? 'BUY' : 'SELL', // Determine action from status
      quantity: json['buyQuantity']?.toDouble(),
      price: json['buyPrice']?.toDouble(),
      amount: json['buyAmount']?.toDouble(),
      profit: json['netProfit']?.toDouble(),
      profitRate: json['totalProfit'] != null && json['buyAmount'] != null && json['buyAmount'] > 0
          ? (json['totalProfit'] / json['buyAmount']).toDouble()
          : 0.0,
      status: json['status'],
      executeTime: json['buyDate'],
      createTime: json['buyDate'],
      updateTime: json['sellDate'],
      remark: json['mentorName'] != null ? 'Mentor: ${json['mentorName']}' : null,
    );
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        return 'Server error: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      default:
        return e.message ?? 'Network error occurred';
    }
  }
}
