import '../../../../core/models/response_result.dart';
import '../models/stock_transaction.dart';

abstract class StockTransactionRepository {
  /// Get stock transaction records with pagination and filters
  Future<ResponseResult<List<StockTransaction>>> getStockTransactionRecords({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? symbol,
    String? action,
    String? startTime,
    String? endTime,
  });
}
