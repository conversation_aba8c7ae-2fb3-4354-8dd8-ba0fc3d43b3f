import 'package:freezed_annotation/freezed_annotation.dart';

part 'stock_transaction.freezed.dart';
part 'stock_transaction.g.dart';

@freezed
class StockTransaction with _$StockTransaction {
  const factory StockTransaction({
    @J<PERSON><PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'orderNo') String? orderNo,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'userId') int? userId,
    @<PERSON>son<PERSON>ey(name: 'symbol') String? symbol,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'symbolName') String? symbolName,
    @Json<PERSON>ey(name: 'action') String? action, // BUY, SELL
    @JsonKey(name: 'quantity') double? quantity,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'price') double? price,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'amount') double? amount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'fee') double? fee,
    @<PERSON>son<PERSON>ey(name: 'profit') double? profit,
    @<PERSON><PERSON><PERSON>ey(name: 'profitRate') double? profitRate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'status') int? status,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'executeTime') String? executeTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime') String? createTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime') String? updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'remark') String? remark,
  }) = _StockTransaction;

  factory StockTransaction.fromJson(Map<String, dynamic> json) =>
      _$StockTransactionFromJson(json);
}

@freezed
class StockTransactionQuery with _$StockTransactionQuery {
  const factory StockTransactionQuery({
    @JsonKey(name: 'pageNum') int? pageNum,
    @JsonKey(name: 'pageSize') int? pageSize,
    @JsonKey(name: 'orderNo') String? orderNo,
    @JsonKey(name: 'symbol') String? symbol,
    @JsonKey(name: 'action') String? action,
    @JsonKey(name: 'startTime') String? startTime,
    @JsonKey(name: 'endTime') String? endTime,
  }) = _StockTransactionQuery;

  factory StockTransactionQuery.fromJson(Map<String, dynamic> json) =>
      _$StockTransactionQueryFromJson(json);
}
