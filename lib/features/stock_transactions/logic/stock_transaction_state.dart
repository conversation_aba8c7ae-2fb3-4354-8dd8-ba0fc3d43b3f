import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/stock_transaction.dart';

part 'stock_transaction_state.freezed.dart';

@freezed
class StockTransactionState with _$StockTransactionState {
  const factory StockTransactionState({
    @Default(false) bool isLoading,
    @Default([]) List<StockTransaction> transactions,
    @Default(null) String? error,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(false) bool hasMoreData,
    @Default(null) String? filterOrderNo,
    @Default(null) String? filterSymbol,
    @Default(null) String? filterAction,
    @Default(null) String? filterStartTime,
    @Default(null) String? filterEndTime,
  }) = _StockTransactionState;
}
