import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/stock_transaction_repository.dart';
import '../domain/models/stock_transaction.dart';
import 'stock_transaction_state.dart';

@injectable
class StockTransactionCubit extends Cubit<StockTransactionState> {
  final StockTransactionRepository _stockTransactionRepository;

  StockTransactionCubit(this._stockTransactionRepository) : super(const StockTransactionState());

  /// Load stock transaction records with pagination
  Future<void> loadTransactions({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? symbol,
    String? action,
    String? startTime,
    String? endTime,
    bool isRefresh = false,
  }) async {
    if (isRefresh) {
      emit(state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 1,
        transactions: [],
      ));
    } else {
      emit(state.copyWith(isLoading: true, error: null));
    }

    try {
      final result = await _stockTransactionRepository.getStockTransactionRecords(
        pageNum: pageNum ?? (isRefresh ? 1 : state.currentPage),
        pageSize: pageSize ?? state.pageSize,
        orderNo: orderNo ?? state.filterOrderNo,
        symbol: symbol ?? state.filterSymbol,
        action: action ?? state.filterAction,
        startTime: startTime ?? state.filterStartTime,
        endTime: endTime ?? state.filterEndTime,
      );

      if (result.isSuccess) {
        final newTransactions = result.data ?? [];
        final updatedTransactions = isRefresh || state.currentPage == 1
            ? newTransactions
            : [...state.transactions, ...newTransactions];

        emit(state.copyWith(
          isLoading: false,
          transactions: updatedTransactions,
          error: null,
          currentPage: (pageNum ?? state.currentPage) + 1,
          hasMoreData: newTransactions.length >= (pageSize ?? state.pageSize),
          filterOrderNo: orderNo ?? state.filterOrderNo,
          filterSymbol: symbol ?? state.filterSymbol,
          filterAction: action ?? state.filterAction,
          filterStartTime: startTime ?? state.filterStartTime,
          filterEndTime: endTime ?? state.filterEndTime,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading stock transactions: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load stock transactions: $e',
      ));
    }
  }

  /// Apply filters and reload transactions
  Future<void> applyFilters({
    String? orderNo,
    String? symbol,
    String? action,
    String? startTime,
    String? endTime,
  }) async {
    await loadTransactions(
      orderNo: orderNo,
      symbol: symbol,
      action: action,
      startTime: startTime,
      endTime: endTime,
      isRefresh: true,
    );
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await loadTransactions(
      orderNo: null,
      symbol: null,
      action: null,
      startTime: null,
      endTime: null,
      isRefresh: true,
    );
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (!state.isLoading && state.hasMoreData) {
      await loadTransactions();
    }
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    await loadTransactions(isRefresh: true);
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(error: null));
  }
}
