import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/wallet_repository.dart';
import '../domain/models/wallet_balance.dart';
import '../domain/models/wallet_transfer.dart';
import 'wallet_state.dart';

@injectable
class WalletCubit extends Cubit<WalletState> {
  final WalletRepository _walletRepository;

  WalletCubit(this._walletRepository) : super(const WalletState());

  Future<void> loadAllBalances() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _walletRepository.getAllBalances();

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoading: false,
          balances: result.data ?? {},
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading balances: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load balances: $e',
      ));
    }
  }

  Future<void> loadBalance(String type) async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _walletRepository.getBalance(type);

      if (result.isSuccess) {
        final updatedBalances = Map<String, WalletBalance>.from(state.balances);
        updatedBalances[type] = result.data!;
        
        emit(state.copyWith(
          isLoading: false,
          balances: updatedBalances,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading balance: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load balance: $e',
      ));
    }
  }

  Future<bool> collectionAmount({
    required double amount,
    required String currency,
    required String payPassword,
  }) async {
    emit(state.copyWith(isCollectionLoading: true, collectionError: null));

    try {
      final request = CollectionAmountRequest(
        amount: amount,
        currency: currency,
        payPassword: payPassword,
      );

      final result = await _walletRepository.collectionAmount(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isCollectionLoading: false,
          collectionError: null,
        ));
        
        // Reload balances after collection
        await loadAllBalances();
        return true;
      } else {
        emit(state.copyWith(
          isCollectionLoading: false,
          collectionError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error collecting amount: $e');
      emit(state.copyWith(
        isCollectionLoading: false,
        collectionError: 'Failed to collect amount: $e',
      ));
      return false;
    }
  }

  Future<void> loadCollectionSupervisor() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final result = await _walletRepository.getCollectionSupervisor();

      if (result.isSuccess) {
        emit(state.copyWith(
          isLoading: false,
          supervisorInfo: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: result.error,
        ));
      }
    } catch (e) {
      log('Error loading supervisor info: $e');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to load supervisor info: $e',
      ));
    }
  }

  Future<bool> transferToUser({
    required String toUsername,
    required double amount,
    required String currency,
    required String payPassword,
    String? remark,
  }) async {
    emit(state.copyWith(isTransferLoading: true, transferError: null));

    try {
      final request = WalletTransferRequest(
        toUsername: toUsername,
        amount: amount,
        currency: currency,
        payPassword: payPassword,
        remark: remark,
      );

      final result = await _walletRepository.transferToUser(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isTransferLoading: false,
          transferError: null,
        ));
        
        // Reload balances after transfer
        await loadAllBalances();
        return true;
      } else {
        emit(state.copyWith(
          isTransferLoading: false,
          transferError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error transferring to user: $e');
      emit(state.copyWith(
        isTransferLoading: false,
        transferError: 'Failed to transfer to user: $e',
      ));
      return false;
    }
  }

  Future<void> validateTransferUsername(String username) async {
    emit(state.copyWith(isUsernameLoading: true, usernameError: null));

    try {
      final result = await _walletRepository.getTransferUsernameInfo(username);

      if (result.isSuccess) {
        emit(state.copyWith(
          isUsernameLoading: false,
          transferUsernameInfo: result.data,
          usernameError: null,
        ));
      } else {
        emit(state.copyWith(
          isUsernameLoading: false,
          usernameError: result.error,
        ));
      }
    } catch (e) {
      log('Error validating username: $e');
      emit(state.copyWith(
        isUsernameLoading: false,
        usernameError: 'Failed to validate username: $e',
      ));
    }
  }

  Future<bool> applyWithdrawal({
    required double amount,
    required String currency,
    required String address,
    required String network,
    required String payPassword,
    String? remark,
  }) async {
    emit(state.copyWith(isWithdrawalLoading: true, withdrawalError: null));

    try {
      final request = WalletWithdrawRequest(
        amount: amount,
        currency: currency,
        address: address,
        network: network,
        payPassword: payPassword,
        remark: remark,
      );

      final result = await _walletRepository.applyWithdrawal(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isWithdrawalLoading: false,
          withdrawalError: null,
        ));
        
        // Reload balances after withdrawal
        await loadAllBalances();
        return true;
      } else {
        emit(state.copyWith(
          isWithdrawalLoading: false,
          withdrawalError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error applying withdrawal: $e');
      emit(state.copyWith(
        isWithdrawalLoading: false,
        withdrawalError: 'Failed to apply withdrawal: $e',
      ));
      return false;
    }
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      collectionError: null,
      transferError: null,
      withdrawalError: null,
      usernameError: null,
    ));
  }

  /// Clear transfer username info
  void clearTransferUsernameInfo() {
    emit(state.copyWith(
      transferUsernameInfo: null,
      usernameError: null,
    ));
  }
}
