import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/wallet_repository.dart';
import '../domain/models/wallet_balance.dart';
import 'wallet_state.dart';

@injectable
class WalletCubit extends Cubit<WalletState> {
  final WalletRepository _walletRepository;

  WalletCubit(this._walletRepository) : super(const WalletState());

  Future<void> loadAllBalances() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.getAllBalances();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        balances: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<void> loadBalance(String type) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.getBalance(type);

    if (result.isSuccess) {
      final updatedBalances = Map<String, WalletBalance>.from(state.balances ?? {});
      updatedBalances[type] = result.data!;
      
      emit(state.copyWith(
        isLoading: false,
        balances: updatedBalances,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  Future<String?> getUsernameByEmail(String email) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.getUsernameByEmail(email);

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        transferUsername: result.data,
        error: null,
      ));
      return result.data;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return null;
    }
  }

  Future<bool> transferToFriend({
    required String email,
    required double amount,
    required String payPassword,
    String? remark,
  }) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.transferToFriend(
      email: email,
      amount: amount,
      payPassword: payPassword,
      remark: remark,
    );

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      // Reload balances after successful transfer
      await loadAllBalances();
      return true;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return false;
    }
  }

  Future<Map<String, dynamic>?> applyWithdrawal({
    required double amount,
    required String address,
    required String payPassword,
    String? remark,
  }) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.applyWithdrawal(
      amount: amount,
      address: address,
      payPassword: payPassword,
      remark: remark,
    );

    if (result.isSuccess) {
      emit(state.copyWith(isLoading: false, error: null));
      // Reload balances after successful withdrawal application
      await loadAllBalances();
      return result.data;
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
      return null;
    }
  }

  Future<void> loadSupervisorInfo() async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await _walletRepository.getSupervisorInfo();

    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        supervisorInfo: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }

  WalletBalance? getBalanceByType(String type) {
    return state.balances?[type];
  }

  double getTotalBalance() {
    if (state.balances == null) return 0.0;
    
    double total = 0.0;
    for (final balance in state.balances!.values) {
      total += balance.balance ?? 0.0;
    }
    return total;
  }

  double getAvailableBalance(String type) {
    final balance = getBalanceByType(type);
    return balance?.availableAmount ?? 0.0;
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }

  void clearTransferUsername() {
    emit(state.copyWith(transferUsername: null));
  }
}
