import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/wallet_balance.dart';

part 'wallet_state.freezed.dart';

@freezed
class WalletState with _$WalletState {
  const factory WalletState({
    @Default(false) bool isLoading,
    @Default(null) Map<String, WalletBalance>? balances,
    @Default(null) String? error,
    @Default(null) String? transferUsername,
    @Default(null) Map<String, dynamic>? supervisorInfo,
  }) = _WalletState;
}
