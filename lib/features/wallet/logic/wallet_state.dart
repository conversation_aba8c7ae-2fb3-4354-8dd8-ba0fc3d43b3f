import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/wallet_balance.dart';
import '../domain/models/wallet_transfer.dart';

part 'wallet_state.freezed.dart';

@freezed
class WalletState with _$WalletState {
  const factory WalletState({
    @Default(false) bool isLoading,
    @Default(false) bool isCollectionLoading,
    @Default(false) bool isTransferLoading,
    @Default(false) bool isWithdrawalLoading,
    @Default(false) bool isUsernameLoading,
    @Default({}) Map<String, WalletBalance> balances,
    @Default(null) String? error,
    @Default(null) String? collectionError,
    @Default(null) String? transferError,
    @Default(null) String? withdrawalError,
    @Default(null) String? usernameError,
    @Default(null) TransferUsernameInfo? transferUsernameInfo,
    @Default(null) CollectionSupervisorInfo? supervisorInfo,
  }) = _WalletState;
}
