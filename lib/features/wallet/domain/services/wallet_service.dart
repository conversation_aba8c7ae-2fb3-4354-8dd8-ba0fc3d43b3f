import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/wallet_repository.dart';
import '../models/wallet_balance.dart';
import '../models/wallet_transfer.dart';

@Injectable(as: WalletRepository)
class WalletService implements WalletRepository {
  final NetworkProvider _networkProvider;

  WalletService(this._networkProvider);

  @override
  Future<ResponseResult<Map<String, WalletBalance>>> getAllBalances() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.balance,
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          final Map<String, dynamic> data = response.data['data'] ?? {};
          final Map<String, WalletBalance> balances = {};
          
          data.forEach((key, value) {
            if (value is Map<String, dynamic>) {
              balances[key] = WalletBalance.fromJson(value);
            }
          });
          
          return ResponseResult.success(balances);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to get balances');
        }
      } else {
        return ResponseResult.error('Failed to get balances');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<WalletBalance>> getBalance(String type) async {
    try {
      final Response response = await _networkProvider.get(
        '/wallet/balance/$type', // Direct path since walletBalanceByType doesn't exist in current endpoints
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(
            WalletBalance.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to get balance');
        }
      } else {
        return ResponseResult.error('Failed to get balance');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> collectionAmount(CollectionAmountRequest request) async {
    try {
      // According to API docs, SettAmountReqVo only requires 'amount' field
      final Response response = await _networkProvider.post(
        ApiEndpoints.collectionWithdraw,
        data: {
          'amount': request.amount,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to collect amount');
        }
      } else {
        return ResponseResult.error('Failed to collect amount');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<CollectionSupervisorInfo>> getCollectionSupervisor() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.collectionSupervisor,
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(
            CollectionSupervisorInfo.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to get supervisor info');
        }
      } else {
        return ResponseResult.error('Failed to get supervisor info');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> transferToUser(WalletTransferRequest request) async {
    try {
      // According to API docs, TransferToFriendReqVo requires email, amount, payPassword, googleCode
      final Response response = await _networkProvider.post(
        ApiEndpoints.collectionTransfer,
        data: {
          'email': request.email,
          'amount': request.amount,
          'payPassword': request.payPassword,
          'googleCode': request.googleCode ?? '',
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to transfer to user');
        }
      } else {
        return ResponseResult.error('Failed to transfer to user');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<TransferUsernameInfo>> getTransferUsernameInfo(String username) async {
    try {
      // According to API docs, this is a GET request with email in body (unusual but that's what the API specifies)
      final Response response = await _networkProvider.get(
        '/wallet/collection/transfer/username', // Direct path since function doesn't exist in current endpoints
        // Note: API docs show email in body for GET request, which is unusual
        // We'll try query parameter first, if it fails we may need to use a different approach
        queryParameters: {'email': username}, // API expects email, not username
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        if (response.data['code'] == 0) {
          // API returns just a string (username), so we create a simple model
          return ResponseResult.success(
            TransferUsernameInfo(
              username: response.data['data'],
              email: username,
              isValid: true,
            ),
          );
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to get username info');
        }
      } else {
        return ResponseResult.error('Failed to get username info');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> applyWithdrawal(WalletWithdrawRequest request) async {
    try {
      // According to API docs, SettOrderReqVo requires addressId, amount, payPassword, googleCode
      final Response response = await _networkProvider.post(
        ApiEndpoints.withdraw,
        data: {
          'addressId': 1, // This should be the ID of a saved address, not the address string
          'amount': request.amount,
          'payPassword': request.payPassword,
          'googleCode': '', // Required field, but can be empty if not using 2FA
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(response.data['msg'] ?? 'Failed to apply withdrawal');
        }
      } else {
        return ResponseResult.error('Failed to apply withdrawal');
      }
    } on DioException catch (e) {
      return ResponseResult.error(e.message ?? 'Network error');
    }
  }
}
