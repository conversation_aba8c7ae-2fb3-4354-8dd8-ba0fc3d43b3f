import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/wallet_repository.dart';
import '../models/wallet_balance.dart';

@Injectable(as: WalletRepository)
class WalletService implements WalletRepository {
  final NetworkProvider _networkProvider;

  WalletService(this._networkProvider);

  @override
  Future<ResponseResult<Map<String, WalletBalance>>> getAllBalances() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.walletBalance,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final Map<String, dynamic> data = response.data['data'] ?? {};
          final Map<String, WalletBalance> balances = {};
          
          data.forEach((key, value) {
            if (value is Map<String, dynamic>) {
              balances[key] = WalletBalance.fromJson(value);
            }
          });
          
          return ResponseResult(data: balances);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get wallet balances');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<WalletBalance>> getBalance(String type) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.walletBalanceByType(type),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: WalletBalance.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get wallet balance');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<bool>> transferToFriend({
    required String email,
    required double amount,
    required String payPassword,
    String? remark,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.walletCollectionTransfer,
        data: {
          'email': email,
          'amount': amount,
          'payPassword': payPassword,
          if (remark != null) 'remark': remark,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to transfer to friend');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<String>> getUsernameByEmail(String email) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.walletCollectionTransferUsername,
        queryParameters: {'email': email},
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: response.data['data']['username'] ?? '',
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get username');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> applyWithdrawal({
    required double amount,
    required String address,
    required String payPassword,
    String? remark,
  }) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.walletCollectionWithdraw,
        data: {
          'amount': amount,
          'address': address,
          'payPassword': payPassword,
          if (remark != null) 'remark': remark,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: response.data['data'] ?? {});
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to apply withdrawal');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }

  @override
  Future<ResponseResult<Map<String, dynamic>>> getSupervisorInfo() async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.walletCollectionSupervisor,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: response.data['data'] ?? {});
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get supervisor info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.message ?? 'Network error');
    }
  }
}
