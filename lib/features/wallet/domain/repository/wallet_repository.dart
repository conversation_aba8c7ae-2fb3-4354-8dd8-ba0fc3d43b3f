import '../../../../core/models/response_result.dart';
import '../models/wallet_balance.dart';

abstract class WalletRepository {
  /// Get all wallet balances
  Future<ResponseResult<Map<String, WalletBalance>>> getAllBalances();
  
  /// Get balance for specific wallet type
  Future<ResponseResult<WalletBalance>> getBalance(String type);
  
  /// Transfer to friend
  Future<ResponseResult<bool>> transferToFriend({
    required String email,
    required double amount,
    required String payPassword,
    String? remark,
  });
  
  /// Get username by email for transfer
  Future<ResponseResult<String>> getUsernameByEmail(String email);
  
  /// Apply for withdrawal
  Future<ResponseResult<Map<String, dynamic>>> applyWithdrawal({
    required double amount,
    required String address,
    required String payPassword,
    String? remark,
  });
  
  /// Get supervisor info
  Future<ResponseResult<Map<String, dynamic>>> getSupervisorInfo();
}
