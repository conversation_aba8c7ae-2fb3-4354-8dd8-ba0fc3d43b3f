import '../../../../core/models/response_result.dart';
import '../models/wallet_balance.dart';
import '../models/wallet_transfer.dart';

abstract class WalletRepository {
  /// Get all wallet balances
  Future<ResponseResult<Map<String, WalletBalance>>> getAllBalances();

  /// Get balance for specific wallet type
  Future<ResponseResult<WalletBalance>> getBalance(String type);

  /// Collection amount operation
  Future<ResponseResult<bool>> collectionAmount(CollectionAmountRequest request);

  /// Get collection supervisor info
  Future<ResponseResult<CollectionSupervisorInfo>> getCollectionSupervisor();

  /// Transfer to another user
  Future<ResponseResult<bool>> transferToUser(WalletTransferRequest request);

  /// Get username info for transfer validation
  Future<ResponseResult<TransferUsernameInfo>> getTransferUsernameInfo(String username);

  /// Apply for withdrawal
  Future<ResponseResult<bool>> applyWithdrawal(WalletWithdrawRequest request);
}
