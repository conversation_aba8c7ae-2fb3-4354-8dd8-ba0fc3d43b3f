import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_balance.freezed.dart';
part 'wallet_balance.g.dart';

@freezed
class WalletBalance with _$WalletBalance {
  const factory WalletBalance({
    @<PERSON><PERSON><PERSON>ey(name: 'type') String? type,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'balance') double? balance,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'currency') String? currency,
    @Json<PERSON>ey(name: 'frozenAmount') double? frozenAmount,
    @Json<PERSON><PERSON>(name: 'availableAmount') double? availableAmount,
    @Json<PERSON><PERSON>(name: 'updateTime') String? updateTime,
  }) = _WalletBalance;

  factory WalletBalance.fromJson(Map<String, dynamic> json) =>
      _$WalletBalanceFromJson(json);
}
