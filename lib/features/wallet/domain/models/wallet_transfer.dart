import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_transfer.freezed.dart';
part 'wallet_transfer.g.dart';

@freezed
class WalletTransferRequest with _$WalletTransferRequest {
  const factory WalletTransferRequest({
    @J<PERSON><PERSON><PERSON>(name: 'email') required String email, // API expects email, not username
    @<PERSON><PERSON><PERSON><PERSON>(name: 'amount') required double amount,
    @<PERSON>son<PERSON><PERSON>(name: 'payPassword') required String payPassword,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'googleCode') String? googleCode, // Required by API
  }) = _WalletTransferRequest;

  factory WalletTransferRequest.fromJson(Map<String, dynamic> json) =>
      _$WalletTransferRequestFromJson(json);
}

@freezed
class WalletWithdrawRequest with _$WalletWithdrawRequest {
  const factory WalletWithdrawRequest({
    @JsonKey(name: 'amount') required double amount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'currency') required String currency,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'address') required String address,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'network') required String network,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'payPassword') required String payPassword,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'remark') String? remark,
  }) = _WalletWithdrawRequest;

  factory WalletWithdrawRequest.fromJson(Map<String, dynamic> json) =>
      _$WalletWithdrawRequestFromJson(json);
}

@freezed
class CollectionAmountRequest with _$CollectionAmountRequest {
  const factory CollectionAmountRequest({
    @JsonKey(name: 'amount') required double amount, // API only requires amount
  }) = _CollectionAmountRequest;

  factory CollectionAmountRequest.fromJson(Map<String, dynamic> json) =>
      _$CollectionAmountRequestFromJson(json);
}

@freezed
class CollectionSupervisorInfo with _$CollectionSupervisorInfo {
  const factory CollectionSupervisorInfo({
    @JsonKey(name: 'supervisorId') int? supervisorId,
    @JsonKey(name: 'supervisorName') String? supervisorName,
    @JsonKey(name: 'supervisorEmail') String? supervisorEmail,
    @JsonKey(name: 'supervisorPhone') String? supervisorPhone,
    @JsonKey(name: 'isActive') @Default(false) bool isActive,
    @JsonKey(name: 'createTime') String? createTime,
  }) = _CollectionSupervisorInfo;

  factory CollectionSupervisorInfo.fromJson(Map<String, dynamic> json) =>
      _$CollectionSupervisorInfoFromJson(json);
}

@freezed
class TransferUsernameInfo with _$TransferUsernameInfo {
  const factory TransferUsernameInfo({
    @JsonKey(name: 'userId') int? userId,
    @JsonKey(name: 'username') String? username,
    @JsonKey(name: 'nickname') String? nickname,
    @JsonKey(name: 'email') String? email,
    @JsonKey(name: 'isValid') @Default(false) bool isValid,
  }) = _TransferUsernameInfo;

  factory TransferUsernameInfo.fromJson(Map<String, dynamic> json) =>
      _$TransferUsernameInfoFromJson(json);
}
