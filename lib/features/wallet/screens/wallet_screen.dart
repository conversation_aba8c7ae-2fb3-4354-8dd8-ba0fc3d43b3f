import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../core/navigation/app_routes.dart';
import '../../../core/navigation/app_router.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/wallet_cubit.dart';
import '../logic/wallet_state.dart';

class WalletScreen extends StatelessWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<WalletCubit>()..loadAllBalances(),
      child: const _WalletView(),
    );
  }
}

class _WalletView extends StatelessWidget {
  const _WalletView();

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Wallet'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => AppRouter.navigateTo(context, AppRoutes.walletHistory),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<WalletCubit>().loadAllBalances();
            },
          ),
        ],
      ),
      body: BlocBuilder<WalletCubit, WalletState>(
        builder: (context, state) {
          if (state.isLoading && state.balances == null) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.error != null && state.balances == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.error!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<WalletCubit>().loadAllBalances();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'My Wallets',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _WalletCard(
                  title: 'Deposit Wallet',
                  balance: _formatBalance(state.balances?['deposit']?.balance),
                  type: 'deposit',
                  onTap: () {},
                ),
                const SizedBox(height: 12),
                _WalletCard(
                  title: 'Profit Wallet',
                  balance: _formatBalance(state.balances?['profit']?.balance),
                  type: 'profit',
                  onTap: () {},
                ),
                const SizedBox(height: 12),
                _WalletCard(
                  title: 'Community Wallet',
                  balance: _formatBalance(state.balances?['community']?.balance),
                  type: 'community',
                  onTap: () {},
                ),
                const SizedBox(height: 12),
                _WalletCard(
                  title: 'Collection Wallet',
                  balance: _formatBalance(state.balances?['collection']?.balance),
                  type: 'collection',
                  onTap: () {},
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => AppRouter.navigateTo(context, AppRoutes.walletTransfer),
                        icon: const Icon(Icons.send),
                        label: const Text('Transfer'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => AppRouter.navigateTo(context, AppRoutes.walletWithdraw),
                        icon: const Icon(Icons.account_balance),
                        label: const Text('Withdraw'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _formatBalance(double? balance) {
    if (balance == null) return '\$0.00';
    return '\$${balance.toStringAsFixed(2)}';
  }
}

class _WalletCard extends StatelessWidget {
  final String title;
  final String balance;
  final String type;
  final VoidCallback onTap;

  const _WalletCard({
    required this.title,
    required this.balance,
    required this.type,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  _getWalletIcon(type),
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      balance,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getWalletIcon(String type) {
    switch (type) {
      case 'deposit':
        return Icons.account_balance_wallet;
      case 'profit':
        return Icons.trending_up;
      case 'community':
        return Icons.group;
      case 'collection':
        return Icons.savings;
      default:
        return Icons.account_balance_wallet;
    }
  }
}
