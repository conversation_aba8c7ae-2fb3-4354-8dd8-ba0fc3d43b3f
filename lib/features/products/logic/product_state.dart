import 'package:freezed_annotation/freezed_annotation.dart';
import '../domain/models/product.dart';
import '../domain/models/mentor.dart';
import '../domain/models/transaction_record.dart';

part 'product_state.freezed.dart';

@freezed
class ProductState with _$ProductState {
  const factory ProductState({
    @Default(false) bool isLoading,
    @Default(false) bool isMentorsLoading,
    @Default(false) bool isProductsLoading,
    @Default(false) bool isOrdersLoading,
    @Default(false) bool isTransactionsLoading,
    @Default(false) bool isPurchasing,
    @Default(false) bool isIncreasing,
    @Default(false) bool isProfitTaking,
    @Default(false) bool isUnbinding,
    @Default([]) List<Mentor> mentors,
    @Default([]) List<Product> products,
    @Default([]) List<ProductOrder> orders,
    @Default([]) List<ProductOrder> rejectedOrders,
    @Default([]) List<TransactionRecord> transactionRecords,
    @Default([]) List<StockTransactionRecord> stockTransactionRecords,
    Mentor? selectedMentor,
    ProductOrder? selectedOrder,
    String? error,
    String? mentorsError,
    String? productsError,
    String? ordersError,
    String? transactionsError,
    String? purchaseError,
    String? increaseError,
    String? profitError,
    String? unbindError,
  }) = _ProductState;
}
