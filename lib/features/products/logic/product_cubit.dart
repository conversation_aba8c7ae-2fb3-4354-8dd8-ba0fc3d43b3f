import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../domain/repository/product_repository.dart';
import '../domain/models/product.dart';
import '../domain/models/mentor.dart';
import '../domain/models/transaction_record.dart';
import 'product_state.dart';

@injectable
class ProductCubit extends Cubit<ProductState> {
  final ProductRepository _productRepository;

  ProductCubit(this._productRepository) : super(const ProductState());

  /// Load mentors list
  Future<void> loadMentors({
    int? pageNum,
    int? pageSize,
    String? name,
    String? company,
  }) async {
    emit(state.copyWith(isMentorsLoading: true, mentorsError: null));

    try {
      final result = await _productRepository.getMentorList(
        pageNum: pageNum,
        pageSize: pageSize,
        name: name,
        company: company,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isMentorsLoading: false,
          mentors: result.data ?? [],
          mentorsError: null,
        ));
      } else {
        emit(state.copyWith(
          isMentorsLoading: false,
          mentorsError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading mentors: $e');
      emit(state.copyWith(
        isMentorsLoading: false,
        mentorsError: 'Failed to load mentors: $e',
      ));
    }
  }

  /// Select a mentor and load their products
  Future<void> selectMentor(Mentor mentor) async {
    emit(state.copyWith(selectedMentor: mentor));
    await loadProducts(mentor.id!);
  }

  /// Load products for a specific mentor
  Future<void> loadProducts(int mentorId) async {
    emit(state.copyWith(isProductsLoading: true, productsError: null));

    try {
      final result = await _productRepository.getProductList(mentorId);

      if (result.isSuccess) {
        emit(state.copyWith(
          isProductsLoading: false,
          products: result.data ?? [],
          productsError: null,
        ));
      } else {
        emit(state.copyWith(
          isProductsLoading: false,
          productsError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading products: $e');
      emit(state.copyWith(
        isProductsLoading: false,
        productsError: 'Failed to load products: $e',
      ));
    }
  }

  /// Purchase a product
  Future<bool> purchaseProduct(ProductPaymentRequest request) async {
    emit(state.copyWith(isPurchasing: true, purchaseError: null));

    try {
      final result = await _productRepository.purchaseProduct(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isPurchasing: false,
          purchaseError: null,
        ));
        
        // Reload orders to show the new purchase
        await loadOrders();
        return true;
      } else {
        emit(state.copyWith(
          isPurchasing: false,
          purchaseError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error purchasing product: $e');
      emit(state.copyWith(
        isPurchasing: false,
        purchaseError: 'Failed to purchase product: $e',
      ));
      return false;
    }
  }

  /// Load user's orders
  Future<void> loadOrders({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  }) async {
    emit(state.copyWith(isOrdersLoading: true, ordersError: null));

    try {
      final result = await _productRepository.getOrderList(
        pageNum: pageNum,
        pageSize: pageSize,
        orderNo: orderNo,
        createTimeStart: createTimeStart,
        createTimeEnd: createTimeEnd,
        processStatus: processStatus,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isOrdersLoading: false,
          orders: result.data ?? [],
          ordersError: null,
        ));
      } else {
        emit(state.copyWith(
          isOrdersLoading: false,
          ordersError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading orders: $e');
      emit(state.copyWith(
        isOrdersLoading: false,
        ordersError: 'Failed to load orders: $e',
      ));
    }
  }

  /// Load rejected orders
  Future<void> loadRejectedOrders({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  }) async {
    emit(state.copyWith(isOrdersLoading: true, ordersError: null));

    try {
      final result = await _productRepository.getRejectedOrderList(
        pageNum: pageNum,
        pageSize: pageSize,
        orderNo: orderNo,
        createTimeStart: createTimeStart,
        createTimeEnd: createTimeEnd,
        processStatus: processStatus,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isOrdersLoading: false,
          rejectedOrders: result.data ?? [],
          ordersError: null,
        ));
      } else {
        emit(state.copyWith(
          isOrdersLoading: false,
          ordersError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading rejected orders: $e');
      emit(state.copyWith(
        isOrdersLoading: false,
        ordersError: 'Failed to load rejected orders: $e',
      ));
    }
  }

  /// Increase contract position
  Future<bool> increaseContract(ProductIncreaseRequest request) async {
    emit(state.copyWith(isIncreasing: true, increaseError: null));

    try {
      final result = await _productRepository.increaseContract(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isIncreasing: false,
          increaseError: null,
        ));
        
        // Reload orders to show updated position
        await loadOrders();
        return true;
      } else {
        emit(state.copyWith(
          isIncreasing: false,
          increaseError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error increasing contract: $e');
      emit(state.copyWith(
        isIncreasing: false,
        increaseError: 'Failed to increase contract: $e',
      ));
      return false;
    }
  }

  /// Take profit
  Future<bool> takeProfit(ProductProfitRequest request) async {
    emit(state.copyWith(isProfitTaking: true, profitError: null));

    try {
      final result = await _productRepository.proposeProfit(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isProfitTaking: false,
          profitError: null,
        ));
        
        // Reload orders to show updated status
        await loadOrders();
        return true;
      } else {
        emit(state.copyWith(
          isProfitTaking: false,
          profitError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error taking profit: $e');
      emit(state.copyWith(
        isProfitTaking: false,
        profitError: 'Failed to take profit: $e',
      ));
      return false;
    }
  }

  /// Unbind product
  Future<bool> unbindProduct(ProductUnbindingRequest request) async {
    emit(state.copyWith(isUnbinding: true, unbindError: null));

    try {
      final result = await _productRepository.unbindProduct(request);

      if (result.isSuccess) {
        emit(state.copyWith(
          isUnbinding: false,
          unbindError: null,
        ));
        
        // Reload orders to show updated status
        await loadOrders();
        return true;
      } else {
        emit(state.copyWith(
          isUnbinding: false,
          unbindError: result.error,
        ));
        return false;
      }
    } catch (e) {
      log('Error unbinding product: $e');
      emit(state.copyWith(
        isUnbinding: false,
        unbindError: 'Failed to unbind product: $e',
      ));
      return false;
    }
  }

  /// Load transaction records for an order
  Future<void> loadTransactionRecords({
    required int orderId,
    int? pageNum,
    int? pageSize,
  }) async {
    emit(state.copyWith(isTransactionsLoading: true, transactionsError: null));

    try {
      final result = await _productRepository.getTransactionRecords(
        orderId: orderId,
        pageNum: pageNum,
        pageSize: pageSize,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          isTransactionsLoading: false,
          transactionRecords: result.data ?? [],
          transactionsError: null,
        ));
      } else {
        emit(state.copyWith(
          isTransactionsLoading: false,
          transactionsError: result.error,
        ));
      }
    } catch (e) {
      log('Error loading transaction records: $e');
      emit(state.copyWith(
        isTransactionsLoading: false,
        transactionsError: 'Failed to load transaction records: $e',
      ));
    }
  }

  /// Clear errors
  void clearErrors() {
    emit(state.copyWith(
      error: null,
      mentorsError: null,
      productsError: null,
      ordersError: null,
      transactionsError: null,
      purchaseError: null,
      increaseError: null,
      profitError: null,
      unbindError: null,
    ));
  }
}
