import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/di/injection.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/product_cubit.dart';
import '../widgets/product_list_widget.dart';
import '../widgets/mentor_selector_widget.dart';

class ProductsScreen extends StatelessWidget {
  const ProductsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BlocProvider(
      create: (context) => getIt<ProductCubit>()..loadMentors(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.products),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              MentorSelectorWidget(),
              <PERSON><PERSON><PERSON><PERSON>(height: 16),
              Expanded(
                child: ProductListWidget(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
