import '../../../../core/models/response_result.dart';
import '../models/product.dart';
import '../models/mentor.dart';
import '../models/transaction_record.dart';

abstract class ProductRepository {
  /// Get product list by mentor ID
  Future<ResponseResult<List<Product>>> getProductList(int mentorId);
  
  /// Get purchased products by mentor ID
  Future<ResponseResult<List<ProductOrder>>> getPurchasedProducts(int mentorId);
  
  /// Purchase a product
  Future<ResponseResult<ProductOrder>> purchaseProduct(ProductPaymentRequest request);
  
  /// Increase contract position
  Future<ResponseResult<bool>> increaseContract(ProductIncreaseRequest request);
  
  /// Propose profit (take profit)
  Future<ResponseResult<bool>> proposeProfit(ProductProfitRequest request);
  
  /// Unbind product order
  Future<ResponseResult<bool>> unbindProduct(ProductUnbindingRequest request);
  
  /// Get order detail by order number
  Future<ResponseResult<ProductOrder>> getOrderDetail(String orderNo);
  
  /// Get order list with pagination and filters
  Future<ResponseResult<List<ProductOrder>>> getOrderList({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  });
  
  /// Get rejected order list
  Future<ResponseResult<List<ProductOrder>>> getRejectedOrderList({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  });
  
  /// Get transaction records for a product order
  Future<ResponseResult<List<TransactionRecord>>> getTransactionRecords({
    required int orderId,
    int? pageNum,
    int? pageSize,
  });
  
  /// Get mentor list with pagination and filters
  Future<ResponseResult<List<Mentor>>> getMentorList({
    int? pageNum,
    int? pageSize,
    String? name,
    String? company,
  });
  
  /// Get stock transaction records
  Future<ResponseResult<List<StockTransactionRecord>>> getStockTransactionRecords({
    int? pageNum,
    int? pageSize,
    String? orderNo,
  });
}
