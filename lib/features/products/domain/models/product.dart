import 'package:freezed_annotation/freezed_annotation.dart';

part 'product.freezed.dart';
part 'product.g.dart';

@freezed
class Product with _$Product {
  const factory Product({
    @JsonKey(name: 'id') int? id,
    @Json<PERSON>ey(name: 'name') String? name, // 产品名称
    @<PERSON><PERSON><PERSON><PERSON>(name: 'type') int? type, // 产品类型
    @J<PERSON><PERSON><PERSON>(name: 'minAmount') double? minAmount,
    @JsonKey(name: 'maxAmount') double? maxAmount,
    @JsonKey(name: 'singleAmount') double? singleAmount, // 单笔金额
    @Json<PERSON>ey(name: 'cycle') int? cycle, // Cycle
    @Json<PERSON>ey(name: 'commissionRate') double? commissionRate, // Commission rate
    @JsonKey(name: 'mentor') String? mentor, // Mentor name
    @JsonKey(name: 'sort') int? sort, // Sort order
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}

@freezed
class ProductOrder with _$ProductOrder {
  const factory ProductOrder({
    @Json<PERSON>ey(name: 'id') int? id,
    @Json<PERSON>ey(name: 'orderNo') String? orderNo,
    @Json<PERSON>ey(name: 'productId') int? productId,
    @JsonKey(name: 'productName') String? productName,
    @JsonKey(name: 'mentorId') int? mentorId,
    @JsonKey(name: 'mentorName') String? mentorName,
    @JsonKey(name: 'amount') double? amount,
    @JsonKey(name: 'price') double? price,
    @JsonKey(name: 'totalAmount') double? totalAmount,
    @JsonKey(name: 'processStatus') int? processStatus,
    @JsonKey(name: 'status') int? status,
    @JsonKey(name: 'createTime') String? createTime,
    @JsonKey(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'startTime') String? startTime,
    @JsonKey(name: 'endTime') String? endTime,
    @JsonKey(name: 'currentProfit') double? currentProfit,
    @JsonKey(name: 'currentProfitRate') double? currentProfitRate,
    @JsonKey(name: 'totalProfit') double? totalProfit,
    @JsonKey(name: 'totalProfitRate') double? totalProfitRate,
    @JsonKey(name: 'remark') String? remark,
  }) = _ProductOrder;

  factory ProductOrder.fromJson(Map<String, dynamic> json) =>
      _$ProductOrderFromJson(json);
}

@freezed
class ProductPaymentRequest with _$ProductPaymentRequest {
  const factory ProductPaymentRequest({
    @JsonKey(name: 'productId') required int productId,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
    @JsonKey(name: 'remark') String? remark,
  }) = _ProductPaymentRequest;

  factory ProductPaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductPaymentRequestFromJson(json);
}

@freezed
class ProductIncreaseRequest with _$ProductIncreaseRequest {
  const factory ProductIncreaseRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductIncreaseRequest;

  factory ProductIncreaseRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductIncreaseRequestFromJson(json);
}

@freezed
class ProductProfitRequest with _$ProductProfitRequest {
  const factory ProductProfitRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductProfitRequest;

  factory ProductProfitRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductProfitRequestFromJson(json);
}

@freezed
class ProductUnbindingRequest with _$ProductUnbindingRequest {
  const factory ProductUnbindingRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductUnbindingRequest;

  factory ProductUnbindingRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductUnbindingRequestFromJson(json);
}
