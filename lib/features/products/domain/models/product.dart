import 'package:freezed_annotation/freezed_annotation.dart';

part 'product.freezed.dart';
part 'product.g.dart';

@freezed
class Product with _$Product {
  const factory Product({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'mentorId') int? mentorId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'name') String? name,
    @Json<PERSON>ey(name: 'description') String? description,
    @Json<PERSON>ey(name: 'price') double? price,
    @<PERSON>son<PERSON>ey(name: 'minAmount') double? minAmount,
    @Json<PERSON>ey(name: 'maxAmount') double? maxAmount,
    @Json<PERSON>ey(name: 'duration') int? duration,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profitRate') double? profitRate,
    @Json<PERSON>ey(name: 'riskLevel') int? riskLevel,
    @Json<PERSON>ey(name: 'status') int? status,
    @<PERSON>son<PERSON>ey(name: 'createTime') String? createTime,
    @<PERSON>son<PERSON>ey(name: 'updateTime') String? updateTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'mentorName') String? mentorName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'mentorAvatar') String? mentorAvatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'totalFollowers') int? totalFollowers,
    @<PERSON>son<PERSON><PERSON>(name: 'currentFollowers') int? currentFollowers,
    @JsonKey(name: 'winRate') double? winRate,
    @JsonKey(name: 'totalProfit') double? totalProfit,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}

@freezed
class ProductOrder with _$ProductOrder {
  const factory ProductOrder({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'orderNo') String? orderNo,
    @JsonKey(name: 'productId') int? productId,
    @JsonKey(name: 'productName') String? productName,
    @JsonKey(name: 'mentorId') int? mentorId,
    @JsonKey(name: 'mentorName') String? mentorName,
    @JsonKey(name: 'amount') double? amount,
    @JsonKey(name: 'price') double? price,
    @JsonKey(name: 'totalAmount') double? totalAmount,
    @JsonKey(name: 'processStatus') int? processStatus,
    @JsonKey(name: 'status') int? status,
    @JsonKey(name: 'createTime') String? createTime,
    @JsonKey(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'startTime') String? startTime,
    @JsonKey(name: 'endTime') String? endTime,
    @JsonKey(name: 'currentProfit') double? currentProfit,
    @JsonKey(name: 'currentProfitRate') double? currentProfitRate,
    @JsonKey(name: 'totalProfit') double? totalProfit,
    @JsonKey(name: 'totalProfitRate') double? totalProfitRate,
    @JsonKey(name: 'remark') String? remark,
  }) = _ProductOrder;

  factory ProductOrder.fromJson(Map<String, dynamic> json) =>
      _$ProductOrderFromJson(json);
}

@freezed
class ProductPaymentRequest with _$ProductPaymentRequest {
  const factory ProductPaymentRequest({
    @JsonKey(name: 'productId') required int productId,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
    @JsonKey(name: 'remark') String? remark,
  }) = _ProductPaymentRequest;

  factory ProductPaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductPaymentRequestFromJson(json);
}

@freezed
class ProductIncreaseRequest with _$ProductIncreaseRequest {
  const factory ProductIncreaseRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductIncreaseRequest;

  factory ProductIncreaseRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductIncreaseRequestFromJson(json);
}

@freezed
class ProductProfitRequest with _$ProductProfitRequest {
  const factory ProductProfitRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'amount') required double amount,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductProfitRequest;

  factory ProductProfitRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductProfitRequestFromJson(json);
}

@freezed
class ProductUnbindingRequest with _$ProductUnbindingRequest {
  const factory ProductUnbindingRequest({
    @JsonKey(name: 'orderNo') required String orderNo,
    @JsonKey(name: 'payPassword') required String payPassword,
  }) = _ProductUnbindingRequest;

  factory ProductUnbindingRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductUnbindingRequestFromJson(json);
}
