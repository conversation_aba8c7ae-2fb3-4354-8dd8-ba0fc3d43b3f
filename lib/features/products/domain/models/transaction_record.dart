import 'package:freezed_annotation/freezed_annotation.dart';

part 'transaction_record.freezed.dart';
part 'transaction_record.g.dart';

@freezed
class TransactionRecord with _$TransactionRecord {
  const factory TransactionRecord({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'orderNo') String? orderNo,
    @J<PERSON><PERSON><PERSON>(name: 'productId') int? productId,
    @Json<PERSON><PERSON>(name: 'productName') String? productName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'mentorId') int? mentorId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'mentorName') String? mentorName,
    @Json<PERSON>ey(name: 'symbol') String? symbol,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'symbolName') String? symbolName,
    @J<PERSON><PERSON><PERSON>(name: 'action') String? action, // BUY, SELL
    @JsonKey(name: 'quantity') double? quantity,
    @Json<PERSON>ey(name: 'price') double? price,
    @<PERSON>son<PERSON>ey(name: 'amount') double? amount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'fee') double? fee,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profit') double? profit,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profitRate') double? profitRate,
    @J<PERSON><PERSON><PERSON>(name: 'status') int? status,
    @J<PERSON><PERSON><PERSON>(name: 'executeTime') String? executeTime,
    @JsonKey(name: 'createTime') String? createTime,
    @JsonKey(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'remark') String? remark,
  }) = _TransactionRecord;

  factory TransactionRecord.fromJson(Map<String, dynamic> json) =>
      _$TransactionRecordFromJson(json);
}

@freezed
class StockTransactionRecord with _$StockTransactionRecord {
  const factory StockTransactionRecord({
    @JsonKey(name: 'id') int? id,
    @JsonKey(name: 'orderNo') String? orderNo,
    @JsonKey(name: 'userId') int? userId,
    @JsonKey(name: 'symbol') String? symbol,
    @JsonKey(name: 'symbolName') String? symbolName,
    @JsonKey(name: 'action') String? action,
    @JsonKey(name: 'quantity') double? quantity,
    @JsonKey(name: 'price') double? price,
    @JsonKey(name: 'amount') double? amount,
    @JsonKey(name: 'fee') double? fee,
    @JsonKey(name: 'profit') double? profit,
    @JsonKey(name: 'profitRate') double? profitRate,
    @JsonKey(name: 'status') int? status,
    @JsonKey(name: 'executeTime') String? executeTime,
    @JsonKey(name: 'createTime') String? createTime,
    @JsonKey(name: 'updateTime') String? updateTime,
  }) = _StockTransactionRecord;

  factory StockTransactionRecord.fromJson(Map<String, dynamic> json) =>
      _$StockTransactionRecordFromJson(json);
}

@freezed
class TransactionRecordQuery with _$TransactionRecordQuery {
  const factory TransactionRecordQuery({
    @JsonKey(name: 'pageNum') int? pageNum,
    @JsonKey(name: 'pageSize') int? pageSize,
    @JsonKey(name: 'orderNo') String? orderNo,
    @JsonKey(name: 'symbol') String? symbol,
    @JsonKey(name: 'action') String? action,
    @JsonKey(name: 'startTime') String? startTime,
    @JsonKey(name: 'endTime') String? endTime,
  }) = _TransactionRecordQuery;

  factory TransactionRecordQuery.fromJson(Map<String, dynamic> json) =>
      _$TransactionRecordQueryFromJson(json);
}
