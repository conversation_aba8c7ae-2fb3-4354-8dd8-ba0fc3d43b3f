import 'package:freezed_annotation/freezed_annotation.dart';

part 'mentor.freezed.dart';
part 'mentor.g.dart';

@freezed
class Mentor with _$Mentor {
  const factory Mentor({
    @Json<PERSON>ey(name: 'id') int? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'name') String? name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'nickname') String? nickname,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar') String? avatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'company') String? company,
    @<PERSON>sonKey(name: 'position') String? position,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'experience') int? experience,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'description') String? description,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'specialization') String? specialization,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'winRate') double? winRate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'totalProfit') double? totalProfit,
    @<PERSON>son<PERSON><PERSON>(name: 'totalFollowers') int? totalFollowers,
    @<PERSON>son<PERSON><PERSON>(name: 'currentFollowers') int? currentFollowers,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'rating') double? rating,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'totalTrades') int? totalTrades,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'successfulTrades') int? successfulTrades,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'averageReturn') double? averageReturn,
    @JsonKey(name: 'maxDrawdown') double? maxDrawdown,
    @JsonKey(name: 'riskScore') int? riskScore,
    @JsonKey(name: 'status') int? status,
    @JsonKey(name: 'createTime') String? createTime,
    @JsonKey(name: 'updateTime') String? updateTime,
    @JsonKey(name: 'lastActiveTime') String? lastActiveTime,
    @JsonKey(name: 'isVerified') @Default(false) bool isVerified,
    @JsonKey(name: 'isActive') @Default(false) bool isActive,
  }) = _Mentor;

  factory Mentor.fromJson(Map<String, dynamic> json) =>
      _$MentorFromJson(json);
}

@freezed
class MentorQuery with _$MentorQuery {
  const factory MentorQuery({
    @JsonKey(name: 'pageNum') int? pageNum,
    @JsonKey(name: 'pageSize') int? pageSize,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'company') String? company,
    @JsonKey(name: 'sortBy') String? sortBy,
    @JsonKey(name: 'sortOrder') String? sortOrder,
  }) = _MentorQuery;

  factory MentorQuery.fromJson(Map<String, dynamic> json) =>
      _$MentorQueryFromJson(json);
}
