import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/models/response_result.dart';
import '../../../../core/network/network_provider.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../repository/product_repository.dart';
import '../models/product.dart';
import '../models/mentor.dart';
import '../models/transaction_record.dart';

@Injectable(as: ProductRepository)
class ProductService implements ProductRepository {
  final NetworkProvider _networkProvider;

  ProductService(this._networkProvider);

  @override
  Future<ResponseResult<List<Product>>> getProductList(int mentorId) async {
    try {
      final Response response = await _networkProvider.get(
        '/product/list/$mentorId', // Use mentorId in path as required by API
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data'] ?? [];
          final products = data.map((json) => Product.fromJson(json)).toList();
          return ResponseResult.success(products);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get product list');
        }
      } else {
        return ResponseResult.error('Failed to get product list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<ProductOrder>>> getPurchasedProducts(int mentorId) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.contractsList,
        queryParameters: {'mentorId': mentorId},
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data'] ?? [];
          final orders = data.map((json) => ProductOrder.fromJson(json)).toList();
          return ResponseResult.success(orders);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get purchased products');
        }
      } else {
        return ResponseResult.error('Failed to get purchased products');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<ProductOrder>> purchaseProduct(ProductPaymentRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.productPay,
        data: request.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final order = ProductOrder.fromJson(responseData['data']);
          return ResponseResult.success(order);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to purchase product');
        }
      } else {
        return ResponseResult.error('Failed to purchase product');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> increaseContract(ProductIncreaseRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.increaseContract,
        data: request.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to increase contract');
        }
      } else {
        return ResponseResult.error('Failed to increase contract');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> proposeProfit(ProductProfitRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.withdrawContract,
        data: request.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to propose profit');
        }
      } else {
        return ResponseResult.error('Failed to propose profit');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<bool>> unbindProduct(ProductUnbindingRequest request) async {
    try {
      final Response response = await _networkProvider.post(
        ApiEndpoints.unbinding,
        data: request.toJson(),
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          return ResponseResult.success(true);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to unbind product');
        }
      } else {
        return ResponseResult.error('Failed to unbind product');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<ProductOrder>> getOrderDetail(String orderNo) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.transactionDetails(orderNo),
        queryParameters: {'orderNo': orderNo},
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final order = ProductOrder.fromJson(responseData['data']);
          return ResponseResult.success(order);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get order detail');
        }
      } else {
        return ResponseResult.error('Failed to get order detail');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<ProductOrder>>> getOrderList({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.productRecords,
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (orderNo != null) 'orderNo': orderNo,
          if (createTimeStart != null) 'createTimeStart': createTimeStart,
          if (createTimeEnd != null) 'createTimeEnd': createTimeEnd,
          if (processStatus != null) 'processStatus': processStatus,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final orders = data.map((json) => ProductOrder.fromJson(json)).toList();
          return ResponseResult.success(orders);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get order list');
        }
      } else {
        return ResponseResult.error('Failed to get order list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<ProductOrder>>> getRejectedOrderList({
    int? pageNum,
    int? pageSize,
    String? orderNo,
    String? createTimeStart,
    String? createTimeEnd,
    int? processStatus,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.productOrderRejectPage(''),
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (orderNo != null) 'orderNo': orderNo,
          if (createTimeStart != null) 'createTimeStart': createTimeStart,
          if (createTimeEnd != null) 'createTimeEnd': createTimeEnd,
          if (processStatus != null) 'processStatus': processStatus,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final orders = data.map((json) => ProductOrder.fromJson(json)).toList();
          return ResponseResult.success(orders);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get rejected order list');
        }
      } else {
        return ResponseResult.error('Failed to get rejected order list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<TransactionRecord>>> getTransactionRecords({
    required int orderId,
    int? pageNum,
    int? pageSize,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        '/product/transaction/$orderId/record', // Direct path since this endpoint doesn't exist in current ApiEndpoints
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final records = data.map((json) => TransactionRecord.fromJson(json)).toList();
          return ResponseResult.success(records);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get transaction records');
        }
      } else {
        return ResponseResult.error('Failed to get transaction records');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<Mentor>>> getMentorList({
    int? pageNum,
    int? pageSize,
    String? name,
    String? company,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.mentorList,
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (name != null) 'name': name,
          if (company != null) 'company': company,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final mentors = data.map((json) => Mentor.fromJson(json)).toList();
          return ResponseResult.success(mentors);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get mentor list');
        }
      } else {
        return ResponseResult.error('Failed to get mentor list');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  @override
  Future<ResponseResult<List<StockTransactionRecord>>> getStockTransactionRecords({
    int? pageNum,
    int? pageSize,
    String? orderNo,
  }) async {
    try {
      final Response response = await _networkProvider.get(
        ApiEndpoints.productTransaction('', 1), // Using function with empty string and page 1
        queryParameters: {
          if (pageNum != null) 'pageNum': pageNum,
          if (pageSize != null) 'pageSize': pageSize,
          if (orderNo != null) 'orderNo': orderNo,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData['code'] == 0) {
          final List<dynamic> data = responseData['data']['records'] ?? [];
          final records = data.map((json) => StockTransactionRecord.fromJson(json)).toList();
          return ResponseResult.success(records);
        } else {
          return ResponseResult.error(responseData['msg'] ?? 'Failed to get stock transaction records');
        }
      } else {
        return ResponseResult.error('Failed to get stock transaction records');
      }
    } on DioException catch (e) {
      return ResponseResult.error(_handleDioError(e));
    } catch (e) {
      return ResponseResult.error('Unexpected error: $e');
    }
  }

  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        return 'Server error: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error. Please check your internet connection.';
      default:
        return e.message ?? 'Network error occurred';
    }
  }
}
