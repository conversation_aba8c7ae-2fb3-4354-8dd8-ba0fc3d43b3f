import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../logic/product_cubit.dart';
import '../logic/product_state.dart';
import '../domain/models/mentor.dart';

class MentorSelectorWidget extends StatelessWidget {
  const MentorSelectorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return BlocBuilder<ProductCubit, ProductState>(
      builder: (context, state) {
        if (state.isMentorsLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (state.mentorsError != null) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    state.mentorsError!,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => context.read<ProductCubit>().loadMentors(),
                    child: Text(l10n.common_retry),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.products_select_mentor,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                if (state.mentors.isEmpty)
                  Text(
                    'No mentors available',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  )
                else
                  DropdownButtonFormField<Mentor>(
                    value: state.selectedMentor,
                    decoration: InputDecoration(
                      border: const OutlineInputBorder(),
                      hintText: l10n.products_select_mentor,
                    ),
                    items: state.mentors.map((mentor) {
                      return DropdownMenuItem<Mentor>(
                        value: mentor,
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 16,
                              backgroundImage: mentor.avatar != null
                                  ? NetworkImage(mentor.avatar!)
                                  : null,
                              child: mentor.avatar == null
                                  ? Text(
                                      mentor.name?.substring(0, 1).toUpperCase() ?? 'M',
                                      style: const TextStyle(fontSize: 12),
                                    )
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    mentor.name ?? 'Unknown',
                                    style: const TextStyle(fontWeight: FontWeight.w500),
                                  ),
                                  if (mentor.company != null)
                                    Text(
                                      mentor.company!,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            if (mentor.winRate != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '${(mentor.winRate! * 100).toStringAsFixed(1)}%',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.green,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (mentor) {
                      if (mentor != null) {
                        context.read<ProductCubit>().selectMentor(mentor);
                      }
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
