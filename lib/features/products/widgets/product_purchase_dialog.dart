import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/models/product.dart';
import '../domain/models/mentor.dart';
import '../logic/product_cubit.dart';
import '../logic/product_state.dart';

class ProductPurchaseDialog extends StatefulWidget {
  final Product product;
  final Mentor mentor;

  const ProductPurchaseDialog({
    super.key,
    required this.product,
    required this.mentor,
  });

  @override
  State<ProductPurchaseDialog> createState() => _ProductPurchaseDialogState();
}

class _ProductPurchaseDialogState extends State<ProductPurchaseDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _payPasswordController = TextEditingController();
  final _remarkController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _amountController.dispose();
    _payPasswordController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return BlocListener<ProductCubit, ProductState>(
      listener: (context, state) {
        if (state.purchaseError != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.purchaseError!),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: AlertDialog(
        title: Text(l10n.products_purchase),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.product.name ?? 'Unknown Product',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Mentor: ${widget.mentor.name ?? 'Unknown'}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Price: \$${widget.product.price?.toStringAsFixed(2) ?? '0.00'}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Amount Input
                TextFormField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: l10n.common_amount,
                    hintText: 'Enter amount to invest',
                    prefixText: '\$ ',
                    border: const OutlineInputBorder(),
                    helperText: widget.product.minAmount != null && widget.product.maxAmount != null
                        ? 'Range: \$${widget.product.minAmount!.toStringAsFixed(0)} - \$${widget.product.maxAmount!.toStringAsFixed(0)}'
                        : null,
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an amount';
                    }
                    
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    
                    if (widget.product.minAmount != null && amount < widget.product.minAmount!) {
                      return 'Amount must be at least \$${widget.product.minAmount!.toStringAsFixed(0)}';
                    }
                    
                    if (widget.product.maxAmount != null && amount > widget.product.maxAmount!) {
                      return 'Amount cannot exceed \$${widget.product.maxAmount!.toStringAsFixed(0)}';
                    }
                    
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Payment Password
                TextFormField(
                  controller: _payPasswordController,
                  decoration: InputDecoration(
                    labelText: 'Payment Password',
                    hintText: 'Enter your payment password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                  ),
                  obscureText: _obscurePassword,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your payment password';
                    }
                    if (value.length < 6) {
                      return 'Payment password must be at least 6 characters';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Remark (Optional)
                TextFormField(
                  controller: _remarkController,
                  decoration: const InputDecoration(
                    labelText: 'Remark (Optional)',
                    hintText: 'Add a note for this purchase',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.common_cancel),
          ),
          BlocBuilder<ProductCubit, ProductState>(
            builder: (context, state) {
              return ElevatedButton(
                onPressed: state.isPurchasing ? null : _handlePurchase,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: state.isPurchasing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(l10n.products_purchase),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _handlePurchase() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    final payPassword = _payPasswordController.text;
    final remark = _remarkController.text.isNotEmpty ? _remarkController.text : null;

    final request = ProductPaymentRequest(
      productId: widget.product.id!,
      amount: amount,
      payPassword: payPassword,
      remark: remark,
    );

    final success = await context.read<ProductCubit>().purchaseProduct(request);
    
    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Product purchased successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
