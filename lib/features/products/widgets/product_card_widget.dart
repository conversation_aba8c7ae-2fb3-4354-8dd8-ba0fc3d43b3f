import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../domain/models/product.dart';
import '../domain/models/mentor.dart';
import '../logic/product_cubit.dart';
import 'product_purchase_dialog.dart';

class ProductCardWidget extends StatelessWidget {
  final Product product;
  final Mentor mentor;

  const ProductCardWidget({
    super.key,
    required this.product,
    required this.mentor,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    product.name ?? 'Unknown Product',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (product.status == 1)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Active',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            
            if (product.description != null) ...[
              const SizedBox(height: 8),
              Text(
                product.description!,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Product Stats
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    l10n.products_profit_rate,
                    product.profitRate != null 
                        ? '${(product.profitRate! * 100).toStringAsFixed(1)}%'
                        : 'N/A',
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    l10n.products_win_rate,
                    mentor.winRate != null 
                        ? '${(mentor.winRate! * 100).toStringAsFixed(1)}%'
                        : 'N/A',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    l10n.products_followers,
                    '${product.currentFollowers ?? 0}/${product.totalFollowers ?? 0}',
                    Colors.orange,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Price and Risk Info
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Price',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        '\$${product.price?.toStringAsFixed(2) ?? '0.00'}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.products_risk_level,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                      Row(
                        children: List.generate(5, (index) {
                          final riskLevel = product.riskLevel ?? 1;
                          return Icon(
                            Icons.star,
                            size: 16,
                            color: index < riskLevel 
                                ? _getRiskColor(riskLevel)
                                : Colors.grey[300],
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Amount Range
            if (product.minAmount != null || product.maxAmount != null) ...[
              Row(
                children: [
                  Text(
                    'Amount Range: ',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '\$${product.minAmount?.toStringAsFixed(0) ?? '0'} - \$${product.maxAmount?.toStringAsFixed(0) ?? '∞'}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Purchase Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: product.status == 1 ? () => _showPurchaseDialog(context) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(l10n.products_purchase),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getRiskColor(int riskLevel) {
    switch (riskLevel) {
      case 1:
      case 2:
        return Colors.green;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showPurchaseDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ProductPurchaseDialog(
        product: product,
        mentor: mentor,
      ),
    );
  }
}
