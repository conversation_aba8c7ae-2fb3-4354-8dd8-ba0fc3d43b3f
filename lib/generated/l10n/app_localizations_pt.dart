// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'SuperFuture';

  @override
  String get common_loading => 'Carregando...';

  @override
  String get common_error => 'Erro';

  @override
  String get common_success => 'Sucesso';

  @override
  String get common_cancel => 'Cancelar';

  @override
  String get common_confirm => 'Confirmar';

  @override
  String get common_save => 'Salvar';

  @override
  String get common_delete => 'Excluir';

  @override
  String get common_edit => 'Editar';

  @override
  String get common_back => 'Voltar';

  @override
  String get common_retry => 'Tentar novamente';

  @override
  String get common_refresh => 'Atualizar';

  @override
  String get common_settings => 'Configurações';

  @override
  String get common_logout => 'Sair';

  @override
  String get common_login => 'Entrar';

  @override
  String get common_password => 'Senha';

  @override
  String get common_email => 'E-mail';

  @override
  String get common_username => 'Nome de usuário';

  @override
  String get common_amount => 'Valor';

  @override
  String get common_balance => 'Saldo';

  @override
  String get common_total => 'Total';

  @override
  String get common_available => 'Disponível';

  @override
  String get common_status => 'Status';

  @override
  String get common_details => 'Detalhes';

  @override
  String get common_yes => 'Sim';

  @override
  String get common_no => 'Não';

  @override
  String get wallet_title => 'Carteira';

  @override
  String get wallet_deposit => 'Carteira de Depósito';

  @override
  String get wallet_profit => 'Carteira de Lucro';

  @override
  String get wallet_community => 'Carteira da Comunidade';

  @override
  String get wallet_collection => 'Carteira de Coleta';

  @override
  String get wallet_transfer => 'Transferir';

  @override
  String get wallet_withdraw => 'Sacar';

  @override
  String get wallet_history => 'Histórico';

  @override
  String get profile_title => 'Perfil';

  @override
  String get profile_edit => 'Editar Perfil';

  @override
  String get profile_change_password => 'Alterar Senha';

  @override
  String get profile_security => 'Segurança';

  @override
  String get settings_title => 'Configurações';

  @override
  String get settings_language => 'Idioma';

  @override
  String get settings_theme => 'Tema';

  @override
  String get settings_notifications => 'Notificações';

  @override
  String get settings_privacy => 'Privacidade';

  @override
  String get settings_about => 'Sobre';

  @override
  String get auth_login_title => 'Entrar';

  @override
  String get auth_register_title => 'Registrar';

  @override
  String get auth_forgot_password => 'Esqueci a Senha';

  @override
  String get auth_captcha => 'Captcha';

  @override
  String get market_title => 'Mercado';

  @override
  String get market_search => 'Buscar Ações';

  @override
  String get market_price => 'Preço';

  @override
  String get market_change => 'Mudança';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
