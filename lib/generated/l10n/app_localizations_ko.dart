// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => '슈퍼퓨처';

  @override
  String get common_loading => '로딩 중...';

  @override
  String get common_error => '오류';

  @override
  String get common_success => '성공';

  @override
  String get common_cancel => '취소';

  @override
  String get common_confirm => '확인';

  @override
  String get common_save => '저장';

  @override
  String get common_delete => '삭제';

  @override
  String get common_edit => '편집';

  @override
  String get common_back => '뒤로';

  @override
  String get common_retry => '재시도';

  @override
  String get common_refresh => '새로고침';

  @override
  String get common_settings => '설정';

  @override
  String get common_logout => '로그아웃';

  @override
  String get common_login => '로그인';

  @override
  String get common_password => '비밀번호';

  @override
  String get common_email => '이메일';

  @override
  String get common_username => '사용자명';

  @override
  String get common_amount => '금액';

  @override
  String get common_balance => '잔액';

  @override
  String get common_total => '총계';

  @override
  String get common_available => '사용 가능';

  @override
  String get common_status => '상태';

  @override
  String get common_details => '세부사항';

  @override
  String get common_yes => '예';

  @override
  String get common_no => '아니오';

  @override
  String get wallet_title => '지갑';

  @override
  String get wallet_deposit => '입금 지갑';

  @override
  String get wallet_profit => '수익 지갑';

  @override
  String get wallet_community => '커뮤니티 지갑';

  @override
  String get wallet_collection => '수집 지갑';

  @override
  String get wallet_transfer => '전송';

  @override
  String get wallet_withdraw => '출금';

  @override
  String get wallet_history => '내역';

  @override
  String get profile_title => '프로필';

  @override
  String get profile_edit => '프로필 편집';

  @override
  String get profile_change_password => '비밀번호 변경';

  @override
  String get profile_security => '보안';

  @override
  String get settings_title => '설정';

  @override
  String get settings_language => '언어';

  @override
  String get settings_theme => '테마';

  @override
  String get settings_notifications => '알림';

  @override
  String get settings_privacy => '개인정보';

  @override
  String get settings_about => '정보';

  @override
  String get auth_login_title => '로그인';

  @override
  String get auth_register_title => '회원가입';

  @override
  String get auth_forgot_password => '비밀번호 찾기';

  @override
  String get auth_captcha => '보안문자';

  @override
  String get market_title => '마켓';

  @override
  String get market_search => '주식 검색';

  @override
  String get market_price => '가격';

  @override
  String get market_change => '변동';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
