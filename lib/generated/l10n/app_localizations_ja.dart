// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'スーパーフューチャー';

  @override
  String get common_loading => '読み込み中...';

  @override
  String get common_error => 'エラー';

  @override
  String get common_success => '成功';

  @override
  String get common_cancel => 'キャンセル';

  @override
  String get common_confirm => '確認';

  @override
  String get common_save => '保存';

  @override
  String get common_delete => '削除';

  @override
  String get common_edit => '編集';

  @override
  String get common_back => '戻る';

  @override
  String get common_retry => '再試行';

  @override
  String get common_refresh => '更新';

  @override
  String get common_settings => '設定';

  @override
  String get common_logout => 'ログアウト';

  @override
  String get common_login => 'ログイン';

  @override
  String get common_password => 'パスワード';

  @override
  String get common_email => 'メール';

  @override
  String get common_username => 'ユーザー名';

  @override
  String get common_amount => '金額';

  @override
  String get common_balance => '残高';

  @override
  String get common_total => '合計';

  @override
  String get common_available => '利用可能';

  @override
  String get common_status => 'ステータス';

  @override
  String get common_details => '詳細';

  @override
  String get common_yes => 'はい';

  @override
  String get common_no => 'いいえ';

  @override
  String get wallet_title => 'Wallet';

  @override
  String get wallet_deposit => 'Deposit Wallet';

  @override
  String get wallet_profit => 'Profit Wallet';

  @override
  String get wallet_community => 'Community Wallet';

  @override
  String get wallet_collection => 'Collection Wallet';

  @override
  String get wallet_transfer => 'Transfer';

  @override
  String get wallet_withdraw => 'Withdraw';

  @override
  String get wallet_history => 'History';

  @override
  String get profile_title => 'Profile';

  @override
  String get profile_edit => 'Edit Profile';

  @override
  String get profile_change_password => 'Change Password';

  @override
  String get profile_security => 'Security';

  @override
  String get settings_title => 'Settings';

  @override
  String get settings_language => 'Language';

  @override
  String get settings_theme => 'Theme';

  @override
  String get settings_notifications => 'Notifications';

  @override
  String get settings_privacy => 'Privacy';

  @override
  String get settings_about => 'About';

  @override
  String get auth_login_title => 'Login';

  @override
  String get auth_register_title => 'Register';

  @override
  String get auth_forgot_password => 'Forgot Password';

  @override
  String get auth_captcha => 'Captcha';

  @override
  String get market_title => 'Market';

  @override
  String get market_search => 'Search Stocks';

  @override
  String get market_price => 'Price';

  @override
  String get market_change => 'Change';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
