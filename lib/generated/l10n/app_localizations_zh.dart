// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '超級未來';

  @override
  String get common_loading => '載入中...';

  @override
  String get common_error => '錯誤';

  @override
  String get common_success => '成功';

  @override
  String get common_cancel => '取消';

  @override
  String get common_confirm => '確認';

  @override
  String get common_save => '儲存';

  @override
  String get common_delete => '刪除';

  @override
  String get common_edit => '編輯';

  @override
  String get common_back => '返回';

  @override
  String get common_retry => '重試';

  @override
  String get common_refresh => '重新整理';

  @override
  String get common_settings => '設定';

  @override
  String get common_logout => '登出';

  @override
  String get common_login => '登入';

  @override
  String get common_password => '密碼';

  @override
  String get common_email => '電子郵件';

  @override
  String get common_username => '使用者名稱';

  @override
  String get common_amount => '金額';

  @override
  String get common_balance => '餘額';

  @override
  String get common_total => '總計';

  @override
  String get common_available => '可用';

  @override
  String get common_status => '狀態';

  @override
  String get common_details => '詳細資料';

  @override
  String get common_yes => '是';

  @override
  String get common_no => '否';

  @override
  String get wallet_title => 'Wallet';

  @override
  String get wallet_deposit => 'Deposit Wallet';

  @override
  String get wallet_profit => 'Profit Wallet';

  @override
  String get wallet_community => 'Community Wallet';

  @override
  String get wallet_collection => 'Collection Wallet';

  @override
  String get wallet_transfer => 'Transfer';

  @override
  String get wallet_withdraw => 'Withdraw';

  @override
  String get wallet_history => 'History';

  @override
  String get profile_title => 'Profile';

  @override
  String get profile_edit => 'Edit Profile';

  @override
  String get profile_change_password => 'Change Password';

  @override
  String get profile_security => 'Security';

  @override
  String get settings_title => 'Settings';

  @override
  String get settings_language => 'Language';

  @override
  String get settings_theme => 'Theme';

  @override
  String get settings_notifications => 'Notifications';

  @override
  String get settings_privacy => 'Privacy';

  @override
  String get settings_about => 'About';

  @override
  String get auth_login_title => 'Login';

  @override
  String get auth_register_title => 'Register';

  @override
  String get auth_forgot_password => 'Forgot Password';

  @override
  String get auth_captcha => 'Captcha';

  @override
  String get market_title => 'Market';

  @override
  String get market_search => 'Search Stocks';

  @override
  String get market_price => 'Price';

  @override
  String get market_change => 'Change';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
