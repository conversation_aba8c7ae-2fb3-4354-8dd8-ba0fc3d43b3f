// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'SuperFuture';

  @override
  String get common_loading => 'Chargement...';

  @override
  String get common_error => 'Erreur';

  @override
  String get common_success => 'Succès';

  @override
  String get common_cancel => 'Annuler';

  @override
  String get common_confirm => 'Confirmer';

  @override
  String get common_save => 'Enregistrer';

  @override
  String get common_delete => 'Supprimer';

  @override
  String get common_edit => 'Modifier';

  @override
  String get common_back => 'Retour';

  @override
  String get common_retry => 'Réessayer';

  @override
  String get common_refresh => 'Actualiser';

  @override
  String get common_settings => 'Paramètres';

  @override
  String get common_logout => 'Déconnexion';

  @override
  String get common_login => 'Connexion';

  @override
  String get common_password => 'Mot de passe';

  @override
  String get common_email => 'E-mail';

  @override
  String get common_username => 'Nom d\'utilisateur';

  @override
  String get common_amount => 'Montant';

  @override
  String get common_balance => 'Solde';

  @override
  String get common_total => 'Total';

  @override
  String get common_available => 'Disponible';

  @override
  String get common_status => 'Statut';

  @override
  String get common_details => 'Détails';

  @override
  String get common_yes => 'Oui';

  @override
  String get common_no => 'Non';

  @override
  String get wallet_title => 'Wallet';

  @override
  String get wallet_deposit => 'Deposit Wallet';

  @override
  String get wallet_profit => 'Profit Wallet';

  @override
  String get wallet_community => 'Community Wallet';

  @override
  String get wallet_collection => 'Collection Wallet';

  @override
  String get wallet_transfer => 'Transfer';

  @override
  String get wallet_withdraw => 'Withdraw';

  @override
  String get wallet_history => 'History';

  @override
  String get profile_title => 'Profile';

  @override
  String get profile_edit => 'Edit Profile';

  @override
  String get profile_change_password => 'Change Password';

  @override
  String get profile_security => 'Security';

  @override
  String get settings_title => 'Settings';

  @override
  String get settings_language => 'Language';

  @override
  String get settings_theme => 'Theme';

  @override
  String get settings_notifications => 'Notifications';

  @override
  String get settings_privacy => 'Privacy';

  @override
  String get settings_about => 'About';

  @override
  String get auth_login_title => 'Login';

  @override
  String get auth_register_title => 'Register';

  @override
  String get auth_forgot_password => 'Forgot Password';

  @override
  String get auth_captcha => 'Captcha';

  @override
  String get market_title => 'Market';

  @override
  String get market_search => 'Search Stocks';

  @override
  String get market_price => 'Price';

  @override
  String get market_change => 'Change';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
