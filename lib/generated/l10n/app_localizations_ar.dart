// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'سوبر فيوتشر';

  @override
  String get common_loading => 'جاري التحميل...';

  @override
  String get common_error => 'خطأ';

  @override
  String get common_success => 'نجح';

  @override
  String get common_cancel => 'إلغاء';

  @override
  String get common_confirm => 'تأكيد';

  @override
  String get common_save => 'حفظ';

  @override
  String get common_delete => 'حذف';

  @override
  String get common_edit => 'تعديل';

  @override
  String get common_back => 'رجوع';

  @override
  String get common_retry => 'إعادة المحاولة';

  @override
  String get common_refresh => 'تحديث';

  @override
  String get common_settings => 'الإعدادات';

  @override
  String get common_logout => 'تسجيل الخروج';

  @override
  String get common_login => 'تسجيل الدخول';

  @override
  String get common_password => 'كلمة المرور';

  @override
  String get common_email => 'البريد الإلكتروني';

  @override
  String get common_username => 'اسم المستخدم';

  @override
  String get common_amount => 'المبلغ';

  @override
  String get common_balance => 'الرصيد';

  @override
  String get common_total => 'المجموع';

  @override
  String get common_available => 'متاح';

  @override
  String get common_status => 'الحالة';

  @override
  String get common_details => 'التفاصيل';

  @override
  String get common_yes => 'نعم';

  @override
  String get common_no => 'لا';

  @override
  String get wallet_title => 'Wallet';

  @override
  String get wallet_deposit => 'Deposit Wallet';

  @override
  String get wallet_profit => 'Profit Wallet';

  @override
  String get wallet_community => 'Community Wallet';

  @override
  String get wallet_collection => 'Collection Wallet';

  @override
  String get wallet_transfer => 'Transfer';

  @override
  String get wallet_withdraw => 'Withdraw';

  @override
  String get wallet_history => 'History';

  @override
  String get profile_title => 'Profile';

  @override
  String get profile_edit => 'Edit Profile';

  @override
  String get profile_change_password => 'Change Password';

  @override
  String get profile_security => 'Security';

  @override
  String get settings_title => 'Settings';

  @override
  String get settings_language => 'Language';

  @override
  String get settings_theme => 'Theme';

  @override
  String get settings_notifications => 'Notifications';

  @override
  String get settings_privacy => 'Privacy';

  @override
  String get settings_about => 'About';

  @override
  String get auth_login_title => 'Login';

  @override
  String get auth_register_title => 'Register';

  @override
  String get auth_forgot_password => 'Forgot Password';

  @override
  String get auth_captcha => 'Captcha';

  @override
  String get market_title => 'Market';

  @override
  String get market_search => 'Search Stocks';

  @override
  String get market_price => 'Price';

  @override
  String get market_change => 'Change';

  @override
  String get products => 'Products';

  @override
  String get products_title => 'Contract Products';

  @override
  String get products_mentor => 'Mentor';

  @override
  String get products_select_mentor => 'Select Mentor';

  @override
  String get products_purchase => 'Purchase';

  @override
  String get products_orders => 'Orders';

  @override
  String get products_increase => 'Increase Position';

  @override
  String get products_take_profit => 'Take Profit';

  @override
  String get products_unbind => 'Unbind';

  @override
  String get products_transaction_records => 'Transaction Records';

  @override
  String get products_order_detail => 'Order Detail';

  @override
  String get products_profit_rate => 'Profit Rate';

  @override
  String get products_win_rate => 'Win Rate';

  @override
  String get products_followers => 'Followers';

  @override
  String get products_risk_level => 'Risk Level';

  @override
  String get products_duration => 'Duration';

  @override
  String get products_min_amount => 'Minimum Amount';

  @override
  String get products_max_amount => 'Maximum Amount';

  @override
  String get products_current_profit => 'Current Profit';

  @override
  String get products_total_profit => 'Total Profit';

  @override
  String get products_order_status => 'Order Status';

  @override
  String get products_process_status => 'Process Status';
}
