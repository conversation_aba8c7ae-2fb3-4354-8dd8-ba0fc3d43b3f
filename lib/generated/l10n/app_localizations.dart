import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_ms.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('ja'),
    Locale('ko'),
    Locale('ms'),
    Locale('pt'),
    Locale('zh'),
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'SuperFuture'**
  String get appName;

  /// No description provided for @common_loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get common_loading;

  /// No description provided for @common_error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get common_error;

  /// No description provided for @common_success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get common_success;

  /// No description provided for @common_cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get common_cancel;

  /// No description provided for @common_confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get common_confirm;

  /// No description provided for @common_save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get common_save;

  /// No description provided for @common_delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get common_delete;

  /// No description provided for @common_edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get common_edit;

  /// No description provided for @common_back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get common_back;

  /// No description provided for @common_retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get common_retry;

  /// No description provided for @common_refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get common_refresh;

  /// No description provided for @common_settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get common_settings;

  /// No description provided for @common_logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get common_logout;

  /// No description provided for @common_login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get common_login;

  /// No description provided for @common_password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get common_password;

  /// No description provided for @common_email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get common_email;

  /// No description provided for @common_username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get common_username;

  /// No description provided for @common_amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get common_amount;

  /// No description provided for @common_balance.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get common_balance;

  /// No description provided for @common_total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get common_total;

  /// No description provided for @common_available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get common_available;

  /// No description provided for @common_status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get common_status;

  /// No description provided for @common_details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get common_details;

  /// No description provided for @common_yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get common_yes;

  /// No description provided for @common_no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get common_no;

  /// No description provided for @wallet_title.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get wallet_title;

  /// No description provided for @wallet_deposit.
  ///
  /// In en, this message translates to:
  /// **'Deposit Wallet'**
  String get wallet_deposit;

  /// No description provided for @wallet_profit.
  ///
  /// In en, this message translates to:
  /// **'Profit Wallet'**
  String get wallet_profit;

  /// No description provided for @wallet_community.
  ///
  /// In en, this message translates to:
  /// **'Community Wallet'**
  String get wallet_community;

  /// No description provided for @wallet_collection.
  ///
  /// In en, this message translates to:
  /// **'Collection Wallet'**
  String get wallet_collection;

  /// No description provided for @wallet_transfer.
  ///
  /// In en, this message translates to:
  /// **'Transfer'**
  String get wallet_transfer;

  /// No description provided for @wallet_withdraw.
  ///
  /// In en, this message translates to:
  /// **'Withdraw'**
  String get wallet_withdraw;

  /// No description provided for @wallet_history.
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get wallet_history;

  /// No description provided for @profile_title.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile_title;

  /// No description provided for @profile_edit.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get profile_edit;

  /// No description provided for @profile_change_password.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get profile_change_password;

  /// No description provided for @profile_security.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get profile_security;

  /// No description provided for @settings_title.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings_title;

  /// No description provided for @settings_language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get settings_language;

  /// No description provided for @settings_theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get settings_theme;

  /// No description provided for @settings_notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get settings_notifications;

  /// No description provided for @settings_privacy.
  ///
  /// In en, this message translates to:
  /// **'Privacy'**
  String get settings_privacy;

  /// No description provided for @settings_about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get settings_about;

  /// No description provided for @auth_login_title.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get auth_login_title;

  /// No description provided for @auth_register_title.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get auth_register_title;

  /// No description provided for @auth_forgot_password.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get auth_forgot_password;

  /// No description provided for @auth_captcha.
  ///
  /// In en, this message translates to:
  /// **'Captcha'**
  String get auth_captcha;

  /// No description provided for @market_title.
  ///
  /// In en, this message translates to:
  /// **'Market'**
  String get market_title;

  /// No description provided for @market_search.
  ///
  /// In en, this message translates to:
  /// **'Search Stocks'**
  String get market_search;

  /// No description provided for @market_price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get market_price;

  /// No description provided for @market_change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get market_change;

  /// No description provided for @products.
  ///
  /// In en, this message translates to:
  /// **'Products'**
  String get products;

  /// No description provided for @products_title.
  ///
  /// In en, this message translates to:
  /// **'Contract Products'**
  String get products_title;

  /// No description provided for @products_mentor.
  ///
  /// In en, this message translates to:
  /// **'Mentor'**
  String get products_mentor;

  /// No description provided for @products_select_mentor.
  ///
  /// In en, this message translates to:
  /// **'Select Mentor'**
  String get products_select_mentor;

  /// No description provided for @products_purchase.
  ///
  /// In en, this message translates to:
  /// **'Purchase'**
  String get products_purchase;

  /// No description provided for @products_orders.
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get products_orders;

  /// No description provided for @products_increase.
  ///
  /// In en, this message translates to:
  /// **'Increase Position'**
  String get products_increase;

  /// No description provided for @products_take_profit.
  ///
  /// In en, this message translates to:
  /// **'Take Profit'**
  String get products_take_profit;

  /// No description provided for @products_unbind.
  ///
  /// In en, this message translates to:
  /// **'Unbind'**
  String get products_unbind;

  /// No description provided for @products_transaction_records.
  ///
  /// In en, this message translates to:
  /// **'Transaction Records'**
  String get products_transaction_records;

  /// No description provided for @products_order_detail.
  ///
  /// In en, this message translates to:
  /// **'Order Detail'**
  String get products_order_detail;

  /// No description provided for @products_profit_rate.
  ///
  /// In en, this message translates to:
  /// **'Profit Rate'**
  String get products_profit_rate;

  /// No description provided for @products_win_rate.
  ///
  /// In en, this message translates to:
  /// **'Win Rate'**
  String get products_win_rate;

  /// No description provided for @products_followers.
  ///
  /// In en, this message translates to:
  /// **'Followers'**
  String get products_followers;

  /// No description provided for @products_risk_level.
  ///
  /// In en, this message translates to:
  /// **'Risk Level'**
  String get products_risk_level;

  /// No description provided for @products_duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get products_duration;

  /// No description provided for @products_min_amount.
  ///
  /// In en, this message translates to:
  /// **'Minimum Amount'**
  String get products_min_amount;

  /// No description provided for @products_max_amount.
  ///
  /// In en, this message translates to:
  /// **'Maximum Amount'**
  String get products_max_amount;

  /// No description provided for @products_current_profit.
  ///
  /// In en, this message translates to:
  /// **'Current Profit'**
  String get products_current_profit;

  /// No description provided for @products_total_profit.
  ///
  /// In en, this message translates to:
  /// **'Total Profit'**
  String get products_total_profit;

  /// No description provided for @products_order_status.
  ///
  /// In en, this message translates to:
  /// **'Order Status'**
  String get products_order_status;

  /// No description provided for @products_process_status.
  ///
  /// In en, this message translates to:
  /// **'Process Status'**
  String get products_process_status;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'ar',
    'de',
    'en',
    'es',
    'fr',
    'hi',
    'ja',
    'ko',
    'ms',
    'pt',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'hi':
      return AppLocalizationsHi();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'ms':
      return AppLocalizationsMs();
    case 'pt':
      return AppLocalizationsPt();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
