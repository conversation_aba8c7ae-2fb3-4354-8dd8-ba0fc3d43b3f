# SuperFuture Flutter App - Project Structure

## Overview
This Flutter application follows a **feature-first clean architecture** pattern with clear separation of concerns, implementing the SuperFuture trading platform API.

## Architecture Highlights

### Core Principles
- **Feature-first structure**: Each feature is self-contained with its own domain, logic, and UI layers
- **Clean Architecture**: Clear separation between domain, data, and presentation layers
- **Dependency Injection**: Using Injectable for DI container management
- **State Management**: Cubit pattern with Freezed models for immutable state
- **Network Layer**: Centralized HTTP client with ResponseResult<T> wrapper
- **WebSocket Integration**: Real-time data streaming capabilities

## Project Structure

```
lib/
├── config/                     # App configuration
│   └── app_config.dart         # Theme, colors, API config
├── core/                       # Core utilities and infrastructure
│   ├── constants/              # App constants
│   │   ├── api_endpoints.dart  # API endpoint definitions
│   │   └── local_storage_keys.dart # Storage key constants
│   ├── di/                     # Dependency injection
│   │   ├── injection.dart      # DI configuration
│   │   └── app_module.dart     # DI modules
│   ├── models/                 # Core models
│   │   └── response_result.dart # Generic API response wrapper
│   ├── network/                # Network layer
│   │   └── network_provider.dart # HTTP client with Dio
│   ├── storage/                # Local storage
│   │   └── secure_storage_helper.dart # Secure storage wrapper
│   ├── utils/                  # Utility classes
│   │   ├── auth_utils.dart     # Authentication utilities
│   │   ├── error_handler.dart  # Error handling utilities
│   │   ├── host_util.dart      # Host management
│   │   └── validators.dart     # Form validation utilities
│   └── websocket/              # WebSocket integration
│       ├── websocket_service.dart # WebSocket service
│       └── websocket_mixin.dart   # WebSocket mixin for easy integration
├── features/                   # Feature modules
│   ├── auth/                   # Authentication feature
│   │   ├── domain/
│   │   │   ├── models/         # Auth models (UserInfo, LoginRequest, Captcha)
│   │   │   ├── repository/     # Auth repository interface
│   │   │   └── services/       # Auth service implementation
│   │   ├── logic/              # State management
│   │   │   ├── auth_cubit.dart # Auth Cubit
│   │   │   └── auth_state.dart # Auth state
│   │   ├── screens/            # Auth screens (placeholder)
│   │   └── widgets/            # Auth widgets (placeholder)
│   ├── market/                 # Market data feature
│   │   └── domain/
│   │       ├── models/         # Market models (StockInfo)
│   │       ├── repository/     # Market repository interface
│   │       └── services/       # Market service implementation
│   ├── profile/                # User profile feature
│   │   └── domain/
│   │       └── models/         # Profile models (UserProfile)
│   └── wallet/                 # Wallet feature
│       └── domain/
│           ├── models/         # Wallet models (WalletBalance)
│           └── repository/     # Wallet repository interface
└── shared/                     # Shared components
    └── widgets/                # Reusable widgets
        ├── loading_widget.dart # Loading indicator
        └── error_widget.dart   # Error display widget
```

## Key Features Implemented

### 1. Authentication System
- **Login/Logout**: Complete authentication flow
- **Captcha Support**: Login captcha integration
- **Token Management**: Secure token storage and auto-refresh
- **User State Management**: Persistent authentication state

### 2. Network Layer
- **HTTP Client**: Dio-based network provider with interceptors
- **Error Handling**: Comprehensive error handling and user-friendly messages
- **Authentication**: Automatic token injection for protected endpoints
- **Response Wrapper**: Generic ResponseResult<T> for consistent API responses

### 3. Market Data Integration
- **Stock Lists**: Paginated stock data with search
- **Real-time Data**: WebSocket integration for live market updates
- **K-line Charts**: Stock chart data endpoints
- **Market Depth**: Order book data
- **Gain Distribution**: Market statistics

### 4. Wallet System
- **Multi-wallet Support**: Different wallet types (deposit, profit, community, collection)
- **Balance Management**: Real-time balance updates
- **Transfer System**: Friend-to-friend transfers
- **Withdrawal**: Withdrawal request system

### 5. WebSocket Integration
- **Real-time Updates**: Live data streaming
- **Auto-reconnection**: Automatic reconnection with exponential backoff
- **Heartbeat**: Connection health monitoring
- **Message Filtering**: Type-based message filtering

## API Integration

### Base Configuration
- **Base URL**: `https://www.superfuture.world`
- **API Path**: `/api`
- **Authentication**: Bearer token in Authorization header
- **Response Format**: `{code: 0, data: {...}, msg: "..."}`

### Key Endpoints Covered
- **Authentication**: Login, logout, captcha, user info
- **Market Data**: Stock lists, K-line, market depth, timeline
- **Wallet**: Balance, transfers, withdrawals
- **Profile**: User profile management
- **File Upload**: Document and image uploads

## State Management

### Cubit Pattern
- **AuthCubit**: Authentication state management
- **Freezed States**: Immutable state objects
- **Error Handling**: Centralized error state management
- **Loading States**: Granular loading indicators

## Security Features

### Data Protection
- **Secure Storage**: Encrypted local storage for sensitive data
- **Token Management**: Automatic token refresh and cleanup
- **Input Validation**: Comprehensive form validation
- **Error Sanitization**: Safe error message display

## Development Setup

### Dependencies
- **State Management**: flutter_bloc, freezed
- **Network**: dio, retrofit
- **DI**: injectable, get_it
- **Storage**: flutter_secure_storage, shared_preferences
- **WebSocket**: web_socket_channel
- **UI**: Material 3, custom theming

### Code Generation
```bash
dart run build_runner build --delete-conflicting-outputs
```

### Analysis
```bash
flutter analyze
```

### Build
```bash
flutter build apk --debug
```

## Next Steps

### UI Implementation
- Implement actual screens and widgets
- Add navigation system
- Create custom UI components
- Implement responsive design

### Additional Features
- Push notifications
- Biometric authentication
- Offline support
- Advanced charting
- Social features

### Testing
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for API flows
- Performance testing

## Architecture Benefits

1. **Scalability**: Easy to add new features without affecting existing code
2. **Maintainability**: Clear separation of concerns and consistent patterns
3. **Testability**: Dependency injection enables easy mocking and testing
4. **Reusability**: Shared components and utilities across features
5. **Type Safety**: Strong typing with Freezed models and proper error handling

This structure provides a solid foundation for building a comprehensive trading platform with real-time capabilities, secure authentication, and robust error handling.
