class ApiEndpoints {
  // Auth
  static const String login = '/login';
  static const String loginCaptcha = '/login/captcha';
  static const String countryCode = '/login/getIpDetail';

  // Register
  static const String register = '/register/';
  static const String captcha = '/register/captcha'; 
  static const String checkCode = '/register/checkCode';
  static const String emailCaptcha = '/register/otp/send';

  // Change Password
  static const String emailSendForgotPassword = '/change_password/email_captcha/send';
  static const String emailCheckForgotPassword = '/change_password/email_captcha/check';
  static const String changePassword = '/change_password';

  // Auth
  static const String auth = '/upload/';
  static const String mobileNo = '/auth/mobile_no/';
  static const String uploadAadhaar = '/upload/ID_CARD';
  static const String submitAadhar = '/auth/identity';
  static const String status = '/auth/status';
  static const String setupPaymentPassword = '/auth/pay_password';
  static const String googleAuth = '/auth/google_token';
  static const String googleAuthCheck = '/auth/google_token/check';

  // Profile
  static const String profileInfo = '/profile/info';
  static const String emailCaptchaProfile = '/profile/email_captcha/send';
  static const String emailCodeCheck = '/profile/email_captcha/check';
  static const String expendAddress = '/profile/expend_address/save';
  static const String profileExpendAddress = '/profile/expend_address';
  static String deleteExpendAddress(int id) => '/profile/expend_address/remove/$id';
  static const String changeWalletPassword = '/profile/pay_password/change';
  static const String changeLoginPassword = '/profile/login_password/change';
  static const String googleAuthReset = '/profile/reset_google_token';
  static const String googleAuthCheckReset = '/profile/reset_google_token/check';

  // Product
  static const String productList = '/product/list';
  static const String contractsList = '/product/purchased';
  static const String productDetail = '/product/detail';
  static const String purchaseInfo = '/product/purchase_info';
  static const String productPay = '/product/pay';
  static const String increaseContract = '/product/increase_contract';
  static const String unbinding = '/product/unbinding';
  static const String withdrawContract = '/product/proposeProfit';
  static const String productRecords = '/product/oderPage';
  static String productOrderRejectPage(String orderNo) => '/product/oderRejectPage?orderNo=$orderNo';
  static String transactionDetails(String id) => '/product/oderDetail?orderNo=$id';

  // Market
  static const String marketList = '/market/ticker24H';
  static String getProductKLine() => '/market/getProductKLine';
  static const String getGainDistribution = '/market/getGainDistribution';
  static const String getStockList = '/market/stockInfo';
  static const String getStockListV2 = '/market/getStockList';
  static const String getComponentStock = '/market/getComponentStock';
  static const String getPlateList = '/market/getPlateList';
  static const String timeLineMini = '/market/timeLine';
  static const String getTimeline = '/market/kline';
  static const String getSearch = '/market/getStockListByKeyword';
  static const String depthQuote = '/market/depth/l2';

  // Stock
  static String productTransaction(String id, int pageNum) =>
      '/stock/transaction/page?orderNo=$id&pageNum=$pageNum&pageSize=10';

  // Mentor
  static String mentorList = '/mentor/info/page';

  // Wallet
  static const String balance = '/wallet/balance';
  static const String depositWallet = '/wallet/balance/deposit';
  static const String cashWallet = '/wallet/balance/cash';
  static const String fundingWalletBalance = '/wallet/balance/cash';
  static const String collectionWithdraw = '/wallet/collection/amount';
  static const String withdraw = '/wallet/collection/withdraw';
  static const String collectionSupervisor = '/wallet/collection/supervisor';
  static const String collectionTransfer = '/wallet/collection/transfer';
  static const String toTrading = '/wallet/transfer';
  static const String createPayOrder = '/wallet/create_payorder';
  static String preview(String amount) => '/wallet/transfer/preview?amount=$amount';
  static String walletRechargeAddress(dynamic paymentType) => '/wallet/$paymentType/address';
  static String recordsWallet(String type, int pageNumber) => '/wallet/$type/history?pageNum=$pageNumber';
  static String collectionTransferUserEmail(String email) => '/wallet/collection/transfer/username?email=$email';
  static const String walletCoins = '/wallet/coins';

  // Article
  static String newsUpdates(int pageNum) => '/article/page?pageNum=$pageNum';
  static const String newsUpdateItem = '/article/details';

  // Community  
  static String community(int pageNum) => '/community/friend/page?pageNum=$pageNum';

  // Announcement
  static const String notifications = '/announcement/info';
  static const String haveRead = '/announcement/have_read';

  // Config
  static const String onlineService = '/config/online_service';
  static const String termsAndConditions = '/config/protocol';
  static const String privacyPolicy = '/config/privacy';
  static const String serviceAgreement = '/config/serviceAgreement';
  static const String benefitRules = '/config/benefitRules';

  // Customer Service
  static const String customerServiceChannels = '/customer-service-channel/list';
  static const String tcUserSig = '/tc/userSig';

  // Carousel
  static const String carouselHomeList = '/carousel/INDEX/list';
  static const String carouselCommunityList = '/carousel/COMMUNITY/list';

  // Finance
  static const String financeAccountInfo = '/finance/product/getAccountInfo';
  static const String financeProductRule = '/finance/product/getFinanceProductRule';
  static const String financeProductBuyList = '/finance/product/getFinanceProductBuyList';
  static const String financeProductBuy = '/finance/product/buy';

  // Agent
  static const String agentCommissionStats = '/agent/getAgentCommissionStats';
  static const String agentSubUsers = '/agent/getAgentSubUsers';

  // App
  static String update(String platform) => '/app/latest/$platform';

  //trade
  static const String withdrawHistory = '/trade/sett/page';

  // User
  static const String orderRateConfig = '/user/orderRate/getConfig';
}
